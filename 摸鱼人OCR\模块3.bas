Option Explicit

' 使用条件编译指令确保代码同时兼容32位和64位Office/VBA环境
#If VBA7 Then
    ' 声明 Windows API 函数
    ' GetDC 用于获取指定窗口的设备上下文句柄(HDC)。传入0表示获取整个屏幕的。
    Private Declare PtrSafe Function GetDC Lib "user32" (ByVal hWnd As LongPtr) As LongPtr
    
    ' ReleaseDC 用于释放由 GetDC 获取的设备上下文句柄，防止资源泄露。
    Private Declare PtrSafe Function ReleaseDC Lib "user32" (ByVal hWnd As LongPtr, ByVal hdc As LongPtr) As Long
    
    ' GetDeviceCaps 用于从设备上下文中检索指定设备的信息，如此处的DPI。
    Private Declare PtrSafe Function GetDeviceCaps Lib "gdi32" (ByVal hdc As LongPtr, ByVal nIndex As Long) As Long
#Else
    ' 适用于旧版VBA (非PtrSafe)
    Private Declare Function GetDC Lib "user32" (ByVal hwnd As Long) As Long
    Private Declare Function ReleaseDC Lib "user32" (ByVal hwnd As Long, ByVal hdc As Long) As Long
    Private Declare Function GetDeviceCaps Lib "gdi32" (ByVal hdc As Long, ByVal nIndex As Long) As Long
#End If

' 定义 GetDeviceCaps 函数所需要的常量
Private Const LOGPIXELSX As Long = 88 ' 水平方向每逻辑英寸的像素数 (Horizontal DPI)
Private Const LOGPIXELSY As Long = 90 ' 垂直方向每逻辑英寸的像素数 (Vertical DPI)

' ===================================================================
' == 公共函数：获取系统DPI
' ===================================================================

'''
' @description 获取系统主显示器的水平DPI
' @return Long 系统水平DPI值，通常为 96, 120, 144 等。获取失败则返回 96。
'''
Public Function GetSystemDpiX() As Long
    Dim hdc As LongPtr
    Dim result As Long
    
    ' 获取屏幕的设备上下文句柄
    hdc = GetDC(0)
    
    If hdc <> 0 Then
        ' 如果成功获取句柄，则查询水平DPI
        result = GetDeviceCaps(hdc, LOGPIXELSX)
        ' 必须释放句柄，否则会造成资源泄露
        Call ReleaseDC(0, hdc)
    Else
        ' 如果获取失败，返回一个标准的默认值
        result = 96
    End If
    
    GetSystemDpiX = result
End Function

'''
' @description 获取系统主显示器的垂直DPI
' @return Long 系统垂直DPI值，通常与水平DPI相同。获取失败则返回 96。
'''
Public Function GetSystemDpiY() As Long
    Dim hdc As LongPtr
    Dim result As Long
    
    ' 获取屏幕的设备上下文句柄
    hdc = GetDC(0)
    
    If hdc <> 0 Then
        ' 如果成功获取句柄，则查询垂直DPI
        result = GetDeviceCaps(hdc, LOGPIXELSY)
        ' 必须释放句柄
        Call ReleaseDC(0, hdc)
    Else
        ' 如果获取失败，返回一个标准的默认值
        result = 96
    End If
    
    GetSystemDpiY = result
End Function

'''
' @description 获取系统的缩放比例 (基于96 DPI)
' @return Double 系统的显示缩放比例，例如 1.0 (100%), 1.25 (125%), 1.5 (150%)
'''
Public Function GetSystemScalingFactor() As Double
    ' 通常X和Y的DPI是相同的，我们用X轴的来计算即可
    GetSystemScalingFactor = GetSystemDpiX / 96#
End Function


' ===================================================================
' == 测试子程序
' ===================================================================

Public Sub Test_GetSystemDPI()
    Dim dpiX As Long
    Dim dpiY As Long
    Dim scaling As Double
    Dim fso As Object
    dpiX = GetSystemDpiX()
    dpiY = GetSystemDpiY()
    scaling = GetSystemScalingFactor()
    Dim txtPath As String
    Dim PluginPath As String
    '获取当前宏（插件）所在目录
    PluginPath = Application.GMSManager.UserGMSPath
    '组合成ORC目录路径
    txtPath = PluginPath & "\orc\dpi.txt"
    Set fso = CreateObject("Scripting.FileSystemObject")
    Debug.Print txtPath
    With fso.CreateTextFile(txtPath, True)
        .WriteLine Format(scaling * 100, "0")
        .Close
    End With
   Debug.Print Format(scaling * 100, "0")
End Sub


Sub 坐标获取_ByRef(ByRef kuan As Double, ByRef gao As Double)
    Dim fso As Object
    Dim ts As Object
    Dim fileContent As String
    Dim Values() As String
    Dim PluginPath As String
    
    ' 声明变量并初始化，防止意外错误
    kuan = 0
    gao = 0
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    ' 假设 Application.GMSManager.UserGMSPath 是有效的路径
    PluginPath = Application.GMSManager.UserGMSPath
    
    ' 组合文件路径
    Dim filePath As String
    filePath = PluginPath & "\orc\坐标信息.txt"
    
    ' 检查文件是否存在
    If Not fso.FileExists(filePath) Then
        MsgBox "错误：找不到坐标文件！" & vbCrLf & filePath, vbCritical
        Exit Sub
    End If
    
    ' 打开并读取文件
    Set ts = fso.OpenTextFile(filePath, 1) ' 1 = ForReading
    fileContent = ts.ReadAll
    ts.Close
    
    ' 分割字符串
    Values = Split(fileContent, ",")
    
    ' 检查数组元素数量是否足够
    If UBound(Values) < 3 Then
        MsgBox "错误：坐标文件格式不正确，至少需要4个值。", vbCritical
        Exit Sub
    End If
    
    ' 计算宽度和高度
    ' 使用 Val 函数比 CDbl 更安全，因为它在转换失败时返回0而不是抛出错误
    kuan = Val(Values(2)) - Val(Values(0))
    gao = Val(Values(3)) - Val(Values(1))
    
    ' 释放对象
    Set ts = Nothing
    Set fso = Nothing
    
End Sub
Public Sub DeleteRectanglesInSelection()
    ' 开启错误处理，防止意外错误中断宏
    On Error Resume Next
    
    ' 将所有操作组合成一个命令组，方便一次性撤销 (Undo)
    ActiveDocument.BeginCommandGroup "删除选中对象中的矩形"
    
    Dim s As Shape
    Dim sel As ShapeRange
    Dim deletedCount As Long
    
    ' 初始化计数器
    deletedCount = 0
    
    ' 获取当前活动文档中的选中对象集合
    Set sel = ActiveSelectionRange
    
    ' 检查是否有对象被选中
    If sel.Count = 0 Then
        MsgBox "请先选择至少一个对象。", vbInformation, "提示"
        ' 结束命令组
        ActiveDocument.EndCommandGroup
        Exit Sub
    End If
    
    ' =========================================================================
    ' 核心逻辑：从后往前遍历选中的对象
    ' 为什么要从后往前？因为当从集合中删除一个项目时，
    ' 集合的总数和后续项目的索引会发生变化。从后往前遍历可以完美避免这个问题。
    ' =========================================================================
    Dim i As Long
    For i = sel.Count To 1 Step -1
        Set s = sel(i)
        
        ' 判断对象的类型是否为矩形 (cdrRectangleShape)
        If s.Type = cdrRectangleShape Then
            s.Delete ' 如果是矩形，则删除
            deletedCount = deletedCount + 1 ' 计数器加 1
        End If
    Next i
    
    ' 结束命令组
    ActiveDocument.EndCommandGroup
    
    ' 根据删除的数量给出不同的用户反馈

    
End Sub

