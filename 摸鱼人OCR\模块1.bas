

Option Explicit
#If VBA7 Then
    Public Declare PtrSafe Sub Sleep Lib "kernel32" (ByVal dwMilliseconds As LongPtr)
#Else
    Public Declare Sub Sleep Lib "kernel32" (ByVal dwMilliseconds As Long)
#End If
'声明 Windows API 函数
Private Declare PtrSafe Function SetForegroundWindow Lib "user32" (ByVal hWnd As LongPtr) As Long
Private Declare PtrSafe Function FindWindow Lib "user32" Alias "FindWindowA" (ByVal lpClassName As String, ByVal lpWindowName As String) As LongPtr
Public Sub SleepMs(ByVal ms As Long)
    Sleep ms
End Sub

Sub SetSelectedTextSpacing100(jj)
Dim hwndCorel As LongPtr
    Dim className As String
    Dim i As Integer
    Dim found As Boolean
    found = False

    '遍历类名后面的数字范围
    For i = 15 To 26
        className = "CorelDRAW" & i
        hwndCorel = FindWindow(className, vbNullString)
        
        If hwndCorel <> 0 Then
            SetForegroundWindow hwndCorel
            found = True
            Exit For
        End If
    Next i

    If Not found Then
        MsgBox "暂不支持该CorelDRAW版本！", vbExclamation
    End If
    Dim s As Shape
    ' 检查当前文档中是否存在选中的对象
    If ActiveSelection.Shapes.Count = 0 Then
        MsgBox "请先选择一个文本对象！", vbExclamation
        Exit Sub
    End If

    ' 遍历所有选中的对象
    For Each s In ActiveSelection.Shapes
        ' 判断选中的对象是否为文本
        If s.Type = cdrTextShape Then
            ' 设置文本字符间距为100%
            s.Text.Story.CharSpacing = jj
            Application.FrameWork.Automation.Invoke "cd77aa9d-e554-4df3-82d7-3d5a7589147e"
            
            ' 模拟键盘输入指定值并按回车键
            SleepMs 200  ' 调用公共模块中的Sleep方法
            SendKeys jj, True
            SleepMs 200          ' 调用公共模块中的Sleep方法
            SendKeys "{ENTER}", True
        End If
    Next s
    Exit Sub

ErrHandler:
    MsgBox "请输入有效的数字并重试！", vbExclamation, "提示"
End Sub

Sub 自动获取间距并统一()
Dim hwndCorel As LongPtr
    Dim className As String
    Dim i As Integer
    Dim found As Boolean
    found = False

    '遍历类名后面的数字范围
    For i = 15 To 26
        className = "CorelDRAW" & i
        hwndCorel = FindWindow(className, vbNullString)
        
        If hwndCorel <> 0 Then
            SetForegroundWindow hwndCorel
            found = True
            Exit For
        End If
    Next i

    If Not found Then
        MsgBox "暂不支持该CorelDRAW版本！", vbExclamation
    End If
    Dim s As Shape
    ' 检查当前文档中是否存在选中的对象
    If ActiveSelection.Shapes.Count = 0 Then
        MsgBox "请先选择一个文本对象！", vbExclamation
        Exit Sub
    End If

    Set s = ActiveShape
    If s Is Nothing Then
        MsgBox "没有选择任何形状。"
        Exit Sub
    End If
    If s.Type = cdrTextShape Then
        Dim tr As TextRange
        Set tr = s.Text.Story
        If Not tr Is Nothing Then
        Else
            MsgBox "无法获取文本范围。"
        End If
    Else
        MsgBox "所选形状不是文本形状。"
    End If
    ' 遍历所有选中的对象
    For Each s In ActiveSelection.Shapes
        ' 判断选中的对象是否为文本
        If s.Type = cdrTextShape Then
            ' 设置文本字符间距为100%

            Application.FrameWork.Automation.Invoke "cd77aa9d-e554-4df3-82d7-3d5a7589147e"
            
            ' 模拟键盘输入指定值并按回车键
            SleepMs 200  ' 调用公共模块中的Sleep方法
            SendKeys tr.CharSpacing, True
            SleepMs 200          ' 调用公共模块中的Sleep方法
            SendKeys "{ENTER}", True
        End If
    Next s
    Exit Sub

ErrHandler:
    MsgBox "请输入有效的数字并重试！", vbExclamation, "提示"
End Sub


