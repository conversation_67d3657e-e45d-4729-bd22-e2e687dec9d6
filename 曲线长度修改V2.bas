Private Sub ModifyCurveLength(ByVal inputValue As Double, ByVal operation As String)
    On Error GoTo ErrorHandler

    Dim s As Shape
    Dim originalLength As Double
    Dim newLength As Double
    Dim scaleFactor As Double
    Dim skippedCount As Integer

    ActiveDocument.Unit = cdrMillimeter
    ActiveDocument.BeginCommandGroup "修改曲线长度"

    skippedCount = 0

    If ActiveSelectionRange.Count = 0 Then
        MsgBox "请先选择要修改的对象", vbExclamation
        Exit Sub
    End If

    Dim curveShapes As ShapeRange
    On Error GoTo CQLError
    Set curveShapes = ActiveSelection.Shapes.FindShapes(Query:="@type = 'curve'")
    On Error GoTo ErrorHandler

    If curveShapes.Count = 0 Then
        MsgBox "没有找到要修改的曲线对象", vbExclamation
        Exit Sub
    End If

    For Each s In curveShapes
        On Error GoTo ShapeError

        originalLength = s.Curve.Length

        Select Case operation
            Case "add"
                newLength = originalLength + inputValue
            Case "reduce"
                newLength = originalLength - inputValue
                If newLength <= 0 Then
                    skippedCount = skippedCount + 1
                    GoTo NextShape
                End If
            Case "set"
                newLength = inputValue
        End Select

        scaleFactor = newLength / originalLength
        s.SetSizeEx s.CenterX, s.CenterY, s.SizeWidth * scaleFactor

        On Error GoTo ErrorHandler
NextShape:
    Next s

    ActiveDocument.EndCommandGroup
    ActiveWindow.Refresh

    If operation = "reduce" And skippedCount > 0 Then
        MsgBox "有 " & skippedCount & " 个曲线对象的长度小于您给出的数值，修改失败", vbExclamation
    End If
    Exit Sub

CQLError:
    On Error Resume Next
    ActiveDocument.EndCommandGroup
    MsgBox "CQL查询错误: " & Err.Description, vbCritical
    Exit Sub

ShapeError:
    MsgBox "处理对象时发生错误: " & Err.Description, vbCritical
    Resume NextShape

ErrorHandler:
    On Error Resume Next
    ActiveDocument.EndCommandGroup
    MsgBox "修改曲线长度时发生错误: " & Err.Description, vbCritical
End Sub

Private Sub CommandButton4_Click()
    On Error GoTo ErrorHandler

    ' 检查是否有选择对象
    If ActiveSelectionRange.Count = 0 Then
        MsgBox "请先选择1个曲线对象后再获取", vbExclamation
        Exit Sub
    End If

    ' 检查是否只选择了1个对象
    If ActiveSelectionRange.Count > 1 Then
        MsgBox "请只选择1个曲线对象", vbExclamation
        Exit Sub
    End If

    ' 检查选择的对象是否为曲线
    Dim selectedShape As Shape
    Set selectedShape = ActiveSelection.Shapes(1)

    If selectedShape.Type <> cdrCurveShape Then
        MsgBox "请先选择1个曲线对象后再获取", vbExclamation
        Exit Sub
    End If

    ' 获取曲线长度并写入TextBox1
    ActiveDocument.Unit = cdrMillimeter
    Dim curveLength As Double
    curveLength = selectedShape.Curve.Length

    ' 将长度值写入TextBox1，保留2位小数
    Me.TextBox1.Text = Format(curveLength, "0.00")

    Exit Sub

ErrorHandler:
    MsgBox "获取曲线长度时发生错误: " & Err.Description, vbCritical
End Sub

Private Sub TextBox1_Change()
    Dim i As Integer
    Dim newText As String
    Dim hasDecimal As Boolean

    newText = ""
    hasDecimal = False

    For i = 1 To Len(Me.TextBox1.Text)
        Dim char As String
        char = Mid(Me.TextBox1.Text, i, 1)

        If char >= "0" And char <= "9" Then
            newText = newText + char
        ElseIf char = "." And Not hasDecimal Then
            newText = newText + char
            hasDecimal = True
        End If
    Next i

    If newText <> Me.TextBox1.Text Then
        Me.TextBox1.Text = newText
    End If
End Sub

Private Sub CommandButton1_Click()
    On Error GoTo ErrorHandler
    If Me.TextBox1.Text = "" Or Not IsNumeric(Me.TextBox1.Text) Then Exit Sub
    Dim addLength As Double
    addLength = CDbl(Me.TextBox1.Text)
    If addLength <= 0 Then Exit Sub
    ModifyCurveLength addLength, "add"
    Exit Sub
ErrorHandler:
    MsgBox "错误: " & Err.Description, vbCritical
End Sub

Private Sub CommandButton2_Click()
    On Error GoTo ErrorHandler
    If Me.TextBox1.Text = "" Or Not IsNumeric(Me.TextBox1.Text) Then Exit Sub
    Dim reduceLength As Double
    reduceLength = CDbl(Me.TextBox1.Text)
    If reduceLength <= 0 Then Exit Sub
    ModifyCurveLength reduceLength, "reduce"
    Exit Sub
ErrorHandler:
    MsgBox "错误: " & Err.Description, vbCritical
End Sub

Private Sub CommandButton3_Click()
    On Error GoTo ErrorHandler
    If Me.TextBox1.Text = "" Or Not IsNumeric(Me.TextBox1.Text) Then Exit Sub
    Dim targetLength As Double
    targetLength = CDbl(Me.TextBox1.Text)
    If targetLength <= 0 Then Exit Sub
    ModifyCurveLength targetLength, "set"
    Exit Sub
ErrorHandler:
    MsgBox "错误: " & Err.Description, vbCritical
End Sub

