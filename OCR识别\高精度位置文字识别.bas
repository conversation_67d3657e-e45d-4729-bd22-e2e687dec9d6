Private ost As Shape, d<PERSON> As Double, stw As Double, sth As Double, maxTop As Double, minLeft As Double, ocrsrl As Double, ocrsrt As Double, ocrsrh As Double

Sub 手写文字识别()
    On Error Resume Next
    ActiveDocument.Unit = cdrMillimeter
    Application.Optimization = False
    ActiveDocument.BeginCommandGroup
    ActiveDocument.ReferencePoint = cdrBottomLeft
    If ActiveDocument Is Nothing Then Exit Sub
    
    If ActiveSelection.Shapes.Count = 0 Then
        MsgBox "出于对GMS性能的怀疑，温馨提醒，注意保存文件。"
        Application.Optimization = False
        ActiveWindow.Refresh: Application.Refresh
        Exit Sub
    End If
    
    Dim st As Shape, tx As Double, ty As Double
    Set ost = ActiveSelection.Shapes(1)
    Set st = ActiveSelection.Shapes(1)
    Dim s As Shape, X As Double, Y As Double, l As Double, t As Double
    tx = st.LeftX
    ty = st.TopY
    X = st.RightX
    Y = st.BottomY
    l = st.LeftX
    t = st.TopY
    ocrsrl = l
    ocrsrt = t
    ocrsrh = st.SizeHeight
    Set s = ActiveLayer.CreateRectangle(l, t, X, Y)
    s.Outline.SetNoOutline
    s.Fill.UniformColor.RGBAssign 128, 128, 0
    s.OrderToBack
    s.AddToSelection
    st.AddToSelection

    Dim bmpo As ShapeRange, bmp As ShapeRange
    Set bmpo = ActiveSelection.Shapes.All
    ActiveDocument.ClearSelection
    Set bmp = bmpo.Duplicate
    bmp.Group
    Dim btm As Shape
    Set btm = bmp.Group
    btm.AddToSelection
    Dim scaleFactor As Double

    If btm.SizeHeight > 2000 Or btm.SizeWidth > 2000 Then
        dpis = 20
    ElseIf btm.SizeHeight > 500 Or btm.SizeWidth > 500 Then
        dpis = 40
    ElseIf btm.SizeHeight > 500 Or btm.SizeWidth > 500 Then
        dpis = 50
    ElseIf btm.SizeHeight > 400 Or btm.SizeWidth > 400 Then
        dpis = 60
    Else
        dpis = 72
    End If
    
    ' 将Bitmap对象保存为图片文件
    Dim tempPath As String
    tempPath = Application.GMSManager.GMSPath & "TempImage.JPEG"

    Set expflt = ActiveDocument.ExportBitmap _
            (tempPath, cdrJPEG, cdrSelection, cdrRGBColorImage, , , dpis, dpis, cdrNormalAntiAliasing, False, False, True, False, cdrCompressionNone)
    With expflt
        .Progressive = False
        .Optimized = False
        .SubFormat = 0
        .Compression = 12
        .Smoothing = 100
        .Finish
    End With
    stw = ost.SizeHeight
    bmp.Delete
    s.Delete

    ' 使用百度OCR API接口识别文本
    Dim apiKey As String
    apiKey = "M4d0JuvcDsnDEnkqblwQiuKf"   ' 替换为自己申请的百度API Key
    Dim secretKey As String
    secretKey = "iAtzNHROHdigwrTlki6113hCHL9psFWz"    ' 替换为自己申请的百度Secret Key

    Dim accessToken As String
    accessToken = GetBaiduAccessToken(apiKey, secretKey)

    If accessToken = "" Then
        MsgBox "无法获取百度OCR Access Token。请检查API Key和Secret Key是否正确。"
        Exit Sub
    End If

    ' 使用百度OCR API识别文本
    Dim ocrResults As Variant
    ocrResults = PerformBaiduOCR(accessToken, tempPath)
    If IsEmpty(ocrResults) Then Exit Sub

    ' 插入识别到的文本到当前文档
    InsertTextShapes ocrResults, dpis  ' 传递dpis参数

    ' 清理临时文件
    Kill tempPath
    
    ActiveDocument.EndCommandGroup
    Application.Optimization = False
    ActiveWindow.Refresh: Application.Refresh
    Exit Sub
ErrorHandler:
    Application.Optimization = False
    ActiveWindow.Refresh: Application.Refresh
    On Error Resume Next
End Sub

Function GetBaiduAccessToken(apiKey As String, secretKey As String) As String
    ' 获取百度OCR Access Token
    On Error Resume Next
    Dim tokenUrl As String
    tokenUrl = "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=" & apiKey & "&client_secret=" & secretKey

    Dim httpReq As Object
    Set httpReq = CreateObject("MSXML2.XMLHTTP")
    httpReq.Open "POST", tokenUrl, False
    httpReq.send

    If httpReq.Status <> 200 Then
        MsgBox "HTTP请求错误：" & httpReq.Status & " - " & httpReq.StatusText
        GetBaiduAccessToken = ""
        Exit Function
    End If

    Dim response As String
    response = httpReq.responseText

    Dim accessToken As String
    accessToken = Split(Split(response, """access_token"":""")(1), """")(0)

    GetBaiduAccessToken = accessToken
End Function

Function PerformBaiduOCR(accessToken As String, imagePath As String) As Variant
    ' 使用百度OCR API识别文本
    On Error Resume Next
    Dim httpReq As Object
    Set httpReq = CreateObject("MSXML2.XMLHTTP")
    
    Dim ocrUrl As String
    ocrUrl = "https://aip.baidubce.com/rest/2.0/ocr/v1/accurate?access_token=" & accessToken

    Dim base64Image As String
    base64Image = ConvertImageToBase64(imagePath)

    Dim postData As String
    postData = "image=" & URLEncode(base64Image)
    
    httpReq.Open "POST", ocrUrl, False
    httpReq.setRequestHeader "Content-Type", "application/x-www-form-urlencoded"
    httpReq.send (postData)

    If httpReq.Status <> 200 Then
        MsgBox "百度OCR API请求错误：" & httpReq.Status & " - " & httpReq.StatusText
        PerformBaiduOCR = Array()
        Exit Function
    End If

    Dim response As String
    response = httpReq.responseText

    PerformBaiduOCR = ParseBaiduOCRResponse(response)
End Function


Function ParseBaiduOCRResponse(response As String) As Variant
    On Error Resume Next
    Dim json As Object
    Set json = JsonConverter.ParseJson(response)

    If json Is Nothing Then
        ParseBaiduOCRResponse = Array()
        Exit Function
    End If

    Dim results As Object
    Set results = json("words_result")

    Dim ocrResults() As Variant
    ReDim ocrResults(0 To results.Count - 1)  ' 改为0-based数组，与摸鱼人项目保持一致

    ' 初始化变量以存储最大top和最小left
    maxTop = 0
    minLeft = 99999

    Dim i As Integer
    For i = 1 To results.Count
        Dim words As String
        words = results(i)("words")

        ' 获取原始像素坐标
        Dim originalTop As Double
        originalTop = results(i)("location")("top")

        Dim originalLeft As Double
        originalLeft = results(i)("location")("left")

        Dim originalWidth As Double
        originalWidth = results(i)("location")("width")

        Dim originalHeight As Double
        originalHeight = results(i)("location")("height")

        ' 计算右下角坐标（类似摸鱼人项目的处理方式）
        Dim rightX As Double
        Dim bottomY As Double
        rightX = originalLeft + originalWidth
        bottomY = originalTop + originalHeight

        ' 计算实际的宽度和高度
        Dim calculatedWidth As Double
        Dim calculatedHeight As Double
        calculatedWidth = rightX - originalLeft
        calculatedHeight = bottomY - originalTop

        ' 更新最大top值和最小left值
        If originalTop > maxTop Then
            maxTop = originalTop
        End If

        If originalLeft < minLeft Then
            minLeft = originalLeft
        End If

        ' 按照摸鱼人项目的数据格式存储：[文本内容, TopY, LeftX, Width, Height]
        ' 使用0-based索引
        Dim itemData(0 To 4) As Variant
        itemData(0) = words                ' 文本内容
        itemData(1) = originalTop          ' 原始Top Y坐标
        itemData(2) = originalLeft         ' 原始Left X坐标
        itemData(3) = calculatedWidth      ' 计算的宽度
        itemData(4) = calculatedHeight     ' 计算的高度

        ocrResults(i - 1) = itemData       ' 存储到0-based数组中
    Next i

    ParseBaiduOCRResponse = ocrResults
End Function

Sub InsertTextShapes(ocrResults As Variant, dpis As Double)
    ' 基本输入验证
    If IsEmpty(ocrResults) Then
         Debug.Print "InsertTextShapes: 接收到的数据为空，无需插入。"
         Exit Sub
    End If

    If Not IsArray(ocrResults) Then
         Debug.Print "InsertTextShapes: 接收到的数据不是一个数组，终止操作。"
         Exit Sub
    End If

    ' 确保有活动文档
    If ActiveDocument Is Nothing Then
        Debug.Print "InsertTextShapes: 没有打开的文档，终止操作。"
        MsgBox "请先打开一个CorelDRAW文档。", vbExclamation, "无活动文档"
        Exit Sub
    End If

    ' 添加错误处理
    On Error GoTo ErrorHandler_InsertTextShapes

    ' 设置单位和参考点
    ActiveDocument.Unit = cdrMillimeter
    ActiveDocument.ReferencePoint = cdrBottomLeft

    Dim textShapes As New ShapeRange ' 用于收集创建的图形以便后续群组
    Dim pageHeight As Double
    ' 获取当前页面的高度，用于转换Y坐标（CorelDRAW使用底部为0的坐标系）
    pageHeight = ActivePage.SizeHeight

    ' 优化：提前设置字体和颜色，避免每次循环中重复设置
    Dim fontName As String: fontName = "黑体" ' 保持原有字体
    Dim textColor As New Color
    textColor.CMYKAssign 15, 100, 100, 0 ' 保持原有颜色设置

    ' 原始数据到CorelDRAW单位的比例因子（采用摸鱼人项目的方式）
    Dim SCALE_FACTOR As Double
    SCALE_FACTOR = 25.4 / dpis  ' 将像素转换为毫米的比例因子

    ' 获取基准形状的坐标（保持原有逻辑）
    Dim baseLeft As Double, baseTop As Double
    baseLeft = ost.LeftX
    baseTop = ost.TopY

    ' 循环处理数据数组
    Dim i As Long
    ' 使用 LBound 和 UBound 确保适应不同维度的数组
    For i = LBound(ocrResults) To UBound(ocrResults)
        Dim currentItemData As Variant ' 当前循环的数据项，期望是一个数组

        ' 验证当前数据项是否是有效的数组且包含足够的元素
        ' 期望数组包含至少 5 个元素 (0 to 4)
        If IsArray(ocrResults(i)) And UBound(ocrResults(i)) - LBound(ocrResults(i)) + 1 >= 5 Then
            ' 使用 On Error Resume Next 仅针对类型转换错误，避免整个循环因一个错误项中断
            On Error Resume Next
            currentItemData = ocrResults(i)

            Dim words As String
            Dim originalTop As Double
            Dim originalLeft As Double
            Dim originalWidth As Double
            Dim originalHeight As Double

            ' 从数据项中提取原始值并尝试转换为正确类型
            words = CStr(currentItemData(0))       ' Text content
            originalTop = CDbl(currentItemData(1)) ' Original Y (Top)
            originalLeft = CDbl(currentItemData(2)) ' Original X (Left)
            originalWidth = CDbl(currentItemData(3)) ' Calculated Width
            originalHeight = CDbl(currentItemData(4)) ' Calculated Height
            ' 关闭临时错误处理
            On Error GoTo ErrorHandler_InsertTextShapes ' 恢复到主错误处理

            ' 检查转换是否失败（Err.Number <> 0）或值是否不合理
            If Err.Number <> 0 Or originalWidth < 0 Or originalHeight < 0 Or words = "" Then
                 ' 重置 Err 对象
                 Err.Clear
                 Debug.Print "警告: 跳过无效、尺寸为负或转换失败的数据项 at index " & i & ": " & words & ", " & originalLeft & ", " & originalTop & ", " & originalWidth & ", " & originalHeight
                 GoTo NextItem ' 跳到下一个循环项
            End If

            ' 应用比例因子到所有尺寸
            Dim scaledTop As Double: scaledTop = originalTop * SCALE_FACTOR
            Dim scaledLeft As Double: scaledLeft = originalLeft * SCALE_FACTOR
            Dim scaledWidth As Double: scaledWidth = originalWidth * SCALE_FACTOR
            Dim scaledHeight As Double: scaledHeight = originalHeight * SCALE_FACTOR

            ' 计算相对于基准形状的最终位置
            Dim finalLeft As Double, finalTop As Double
            finalLeft = baseLeft + scaledLeft
            finalTop = baseTop - scaledTop  ' Y坐标向上为正

            ' CorelDRAW 的 CreateArtisticText(X, Y, Text) 方法
            Dim s As Shape
            Set s = ActiveLayer.CreateArtisticText(finalLeft, finalTop, words, cdrSimplifiedChinese, , , 12)

            ' 设置文本属性 (字体和颜色)
            ' 检查图形对象是否成功创建
            If Not s Is Nothing Then
                s.Text.Story.Fill.UniformColor = textColor ' 设置字体颜色
                ' 尝试设置字体，字体名称可能需要精确匹配系统中的字体
                On Error Resume Next ' 忽略字体设置错误（如字体不存在）
                s.Text.Story.Font = fontName
                On Error GoTo ErrorHandler_InsertTextShapes ' 恢复主错误处理

                ' 设置图形的尺寸（采用摸鱼人项目的方式）
                s.SizeWidth = scaledWidth
                s.SizeHeight = scaledHeight

                ' 再次设置图形的位置，确保其左上角与计算出的位置对齐
                s.LeftX = finalLeft
                s.TopY = finalTop

                ' 将新创建的文本图形添加到 ShapeRange 集合中，以便后续群组
                textShapes.Add s
            Else
                 Debug.Print "警告: 无法为数据项 at index " & i & " 创建图形: " & words
            End If

        Else
             ' 警告格式不符的数据项
             Debug.Print "警告: 跳过格式不符的数据项 at index " & i
        End If

NextItem: ' 跳到下一个循环项的标签
    Next i ' 处理下一个数据项

    ' 在命令组结束后进行群组操作
    If textShapes.Count > 0 Then
        Dim groupedShape As Shape
        Set groupedShape = textShapes.Group
        groupedShape.CreateSelection
    End If

    ' 正常退出子程序
    Exit Sub

' 错误处理标签
ErrorHandler_InsertTextShapes:
    ' 在这里处理发生的错误
    MsgBox "在 InsertTextShapes 宏执行过程中发生错误:" & vbCrLf & _
           "错误号: " & Err.Number & vbCrLf & _
           "描述: " & Err.Description & vbCrLf & vbCrLf & _
           "请检查您的数据格式或CorelDRAW状态。", vbCritical, "运行时错误"

    ' 清理 Err 对象
    Err.Clear

    ' 退出子程序
    Exit Sub

End Sub
Function URLEncode(DecodeString As String) As String
    Dim i As Long ' 使用 Long 类型来处理较大的索引值
    Dim CharCode As Integer
    Dim Result As String
    Dim ChunkSize As Integer
    Dim StartIndex As Long ' 使用 Long 类型来处理较大的索引值
    Dim EndIndex As Long ' 使用 Long 类型来处理较大的索引值

    ChunkSize = 10 ' 进一步减小每个分段的大小

    For StartIndex = 1 To Len(DecodeString) Step ChunkSize
        EndIndex = StartIndex + ChunkSize - 1
        If EndIndex > Len(DecodeString) Then
            EndIndex = Len(DecodeString)
        End If

        For i = StartIndex To EndIndex
            CharCode = Asc(Mid(DecodeString, i, 1))
            If (CharCode >= 48 And CharCode <= 57) Or _
               (CharCode >= 65 And CharCode <= 90) Or _
               (CharCode >= 97 And CharCode <= 122) Or _
               CharCode = 45 Or CharCode = 46 Or _
               CharCode = 95 Or CharCode = 126 Then
                Result = Result & Chr(CharCode)
            Else
                Result = Result & "%" & Hex(CharCode)
            End If
        Next i
    Next StartIndex

    URLEncode = Result
End Function

Function ConvertImageToBase64(imagePath As String) As String
    Dim b64 As String
    Dim objStream As Object

    Set objStream = CreateObject("ADODB.Stream")
    objStream.Type = 1 ' Binary
    objStream.Open
    objStream.LoadFromFile imagePath

    Dim bytes() As Byte
    bytes = objStream.Read

    b64 = EncodeBase64(bytes)

    ConvertImageToBase64 = b64

    objStream.Close
End Function

Function EncodeBase64(data() As Byte) As String
    Dim xml As Object
    Dim node As Object

    Set xml = CreateObject("MSXML2.DOMDocument")
    Set node = xml.createElement("b64")
    node.DataType = "bin.base64"
    node.nodeTypedValue = data
    EncodeBase64 = node.Text

    EncodeBase64 = Replace(node.Text, vbLf, "")
End Function