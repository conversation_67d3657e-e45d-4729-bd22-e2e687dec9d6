Private ost As Shape, d<PERSON> As Double, stw As Double, sth As Double, maxTop As Double, minLeft As Double, ocrsrl As Double, ocrsrt As Double, ocrsrh As Double

Sub 手写文字识别()
    On Error Resume Next
    ActiveDocument.Unit = cdrMillimeter
    Application.Optimization = False
    ActiveDocument.BeginCommandGroup
    ActiveDocument.ReferencePoint = cdrBottomLeft
    If ActiveDocument Is Nothing Then Exit Sub
    
    If ActiveSelection.Shapes.Count = 0 Then
        MsgBox "出于对GMS性能的怀疑，温馨提醒，注意保存文件。"
        Application.Optimization = False
        ActiveWindow.Refresh: Application.Refresh
        Exit Sub
    End If
    
    Dim st As Shape, tx As Double, ty As Double
    Set ost = ActiveSelection.Shapes(1)
    Set st = ActiveSelection.Shapes(1)
    Dim s As Shape, X As Double, Y As Double, l As Double, t As Double
    tx = st.LeftX
    ty = st.TopY
    X = st.RightX
    Y = st.BottomY
    l = st.LeftX
    t = st.TopY
    ocrsrl = l
    ocrsrt = t
    ocrsrh = st.SizeHeight
    Set s = ActiveLayer.CreateRectangle(l, t, X, Y)
    s.Outline.SetNoOutline
    s.Fill.UniformColor.RGBAssign 128, 128, 0
    s.OrderToBack
    s.AddToSelection
    st.AddToSelection

    Dim bmpo As ShapeRange, bmp As ShapeRange
    Set bmpo = ActiveSelection.Shapes.All
    ActiveDocument.ClearSelection
    Set bmp = bmpo.Duplicate
    bmp.Group
    Dim btm As Shape
    Set btm = bmp.Group
    btm.AddToSelection
    Dim scaleFactor As Double

    If btm.SizeHeight > 2000 Or btm.SizeWidth > 2000 Then
        dpis = 20
    ElseIf btm.SizeHeight > 500 Or btm.SizeWidth > 500 Then
        dpis = 40
    ElseIf btm.SizeHeight > 500 Or btm.SizeWidth > 500 Then
        dpis = 50
    ElseIf btm.SizeHeight > 400 Or btm.SizeWidth > 400 Then
        dpis = 60
    Else
        dpis = 72
    End If
    
    ' 将Bitmap对象保存为图片文件
    Dim tempPath As String
    tempPath = Application.GMSManager.GMSPath & "TempImage.JPEG"

    Set expflt = ActiveDocument.ExportBitmap _
            (tempPath, cdrJPEG, cdrSelection, cdrRGBColorImage, , , dpis, dpis, cdrNormalAntiAliasing, False, False, True, False, cdrCompressionNone)
    With expflt
        .Progressive = False
        .Optimized = False
        .SubFormat = 0
        .Compression = 12
        .Smoothing = 100
        .Finish
    End With
    stw = ost.SizeHeight
    bmp.Delete
    s.Delete

    ' 使用百度OCR API接口识别文本
    Dim apiKey As String
    apiKey = "M4d0JuvcDsnDEnkqblwQiuKf"   ' 替换为自己申请的百度API Key
    Dim secretKey As String
    secretKey = "iAtzNHROHdigwrTlki6113hCHL9psFWz"    ' 替换为自己申请的百度Secret Key

    Dim accessToken As String
    accessToken = GetBaiduAccessToken(apiKey, secretKey)

    If accessToken = "" Then
        MsgBox "无法获取百度OCR Access Token。请检查API Key和Secret Key是否正确。"
        Exit Sub
    End If

    ' 使用百度OCR API识别文本
    Dim ocrResults As Variant
    ocrResults = PerformBaiduOCR(accessToken, tempPath)
    If IsEmpty(ocrResults) Then Exit Sub

    ' 插入识别到的文本到当前文档
    InsertTextShapes ocrResults, dpis  ' 传递dpis参数

    ' 清理临时文件
    Kill tempPath
    
    ActiveDocument.EndCommandGroup
    Application.Optimization = False
    ActiveWindow.Refresh: Application.Refresh
    Exit Sub
ErrorHandler:
    Application.Optimization = False
    ActiveWindow.Refresh: Application.Refresh
    On Error Resume Next
End Sub

Function GetBaiduAccessToken(apiKey As String, secretKey As String) As String
    ' 获取百度OCR Access Token
    On Error Resume Next
    Dim tokenUrl As String
    tokenUrl = "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=" & apiKey & "&client_secret=" & secretKey

    Dim httpReq As Object
    Set httpReq = CreateObject("MSXML2.XMLHTTP")
    httpReq.Open "POST", tokenUrl, False
    httpReq.send

    If httpReq.Status <> 200 Then
        MsgBox "HTTP请求错误：" & httpReq.Status & " - " & httpReq.StatusText
        GetBaiduAccessToken = ""
        Exit Function
    End If

    Dim response As String
    response = httpReq.responseText

    Dim accessToken As String
    accessToken = Split(Split(response, """access_token"":""")(1), """")(0)

    GetBaiduAccessToken = accessToken
End Function

Function PerformBaiduOCR(accessToken As String, imagePath As String) As Variant
    ' 使用百度OCR API识别文本
    On Error Resume Next
    Dim httpReq As Object
    Set httpReq = CreateObject("MSXML2.XMLHTTP")
    
    Dim ocrUrl As String
    ocrUrl = "https://aip.baidubce.com/rest/2.0/ocr/v1/handwriting?access_token=" & accessToken

    Dim base64Image As String
    base64Image = ConvertImageToBase64(imagePath)

    Dim postData As String
    postData = "image=" & URLEncode(base64Image)
    
    httpReq.Open "POST", ocrUrl, False
    httpReq.setRequestHeader "Content-Type", "application/x-www-form-urlencoded"
    httpReq.send (postData)

    If httpReq.Status <> 200 Then
        MsgBox "百度OCR API请求错误：" & httpReq.Status & " - " & httpReq.StatusText
        PerformBaiduOCR = Array()
        Exit Function
    End If

    Dim response As String
    response = httpReq.responseText

    PerformBaiduOCR = ParseBaiduOCRResponse(response)
End Function

Function ParseBaiduOCRResponse0(response As String) As Variant
    ' 解析百度OCR API响应
    On Error Resume Next
    Dim json As Object
    Set json = JsonConverter.ParseJson(response)

    If json Is Nothing Then
        ParseBaiduOCRResponse = Array()
        Exit Function
    End If
    
    Dim results As Object
    Set results = json("words_result")
    
    Dim ocrResults() As Variant
    ReDim ocrResults(1 To results.Count)
    
    Dim i As Integer
    For i = 1 To results.Count
        Dim words As String
        words = results(i)("words")

        Dim top As Integer
        top = results(i)("location")("top")
        
        Dim left As Integer
        left = results(i)("location")("left")
        
        Dim width As Integer
        width = results(i)("location")("width")
        
        Dim height As Integer
        height = results(i)("location")("height")
        
        ocrResults(i) = Array(words, top, left, width, height)
    Next i
    
    ParseBaiduOCRResponse = ocrResults
End Function
Function ParseBaiduOCRResponse(response As String) As Variant
    On Error Resume Next
    Dim json As Object
    Set json = JsonConverter.ParseJson(response)

    If json Is Nothing Then
        ParseBaiduOCRResponse = Array()
        Exit Function
    End If
    
    Dim results As Object
    Set results = json("words_result")
    
    Dim ocrResults() As Variant
    ReDim ocrResults(1 To results.Count)
    
    ' 初始化变量以存储最大top和最小left
    maxTop = 0
    minLeft = 99999
    
    Dim i As Integer
    For i = 1 To results.Count
        Dim words As String
        words = results(i)("words")

        Dim top As Double
        top = results(i)("location")("top") / dpis * 25.4
        
        Dim left As Double
        left = results(i)("location")("left") / dpis * 25.4
        
        Dim width As Double
        width = results(i)("location")("width") / dpis * 25.4
        
        Dim height As Double
        height = results(i)("location")("height") / dpis * 25.4
        
        Dim bottom As Double
        bottom = top - height + ocrsrh
        
        ' 更新最大top值和最小left值
        If top > maxTop Then
            maxTop = top
        End If
        
        If left < minLeft Then
            minLeft = left
        End If
        
        ' 将识别结果存入数组
        ocrResults(i) = Array(words, bottom, left, width, height)
    Next i
    
    ParseBaiduOCRResponse = ocrResults
End Function

Sub InsertTextShapes(ocrResults As Variant, dpis As Double)
    ActiveDocument.Unit = cdrMillimeter
    ActiveDocument.ReferencePoint = cdrBottomLeft
    If ActiveDocument Is Nothing Then Exit Sub

    ' 获取基准形状的左上角坐标
    Dim shapeLeft As Double
    Dim shapeTop As Double
    shapeLeft = ost.LeftX
    shapeTop = ost.TopY

    Dim sr As ShapeRange
    Set sr = ActiveSelection.Shapes.All

    ' 插入识别到的文本到当前文档
    Dim i As Integer
    Dim srt As New ShapeRange
    Dim offl As Double, offh As Double
    
    For i = LBound(ocrResults) To UBound(ocrResults)
        Dim textData() As Variant
        textData = ocrResults(i)

        Dim words As String
        words = textData(0)
        
        Dim bottom As Double
        bottom = textData(1)
        
        Dim left As Double
        left = textData(2)
        
        Dim widthtext As Double
        widthtext = textData(3)
        
        Dim heighttext As Double
        heighttext = textData(4)
        
        If i = 1 Then
            offh = textData(4)
            offl = textData(2)
        End If
        
        ' 创建文本形状
        Dim textShape As Shape
        Set textShape = ActiveLayer.CreateArtisticText(left, -bottom, words, cdrSimplifiedChinese, , , 12)
        textShape.SetSize widthtext, 0
        ActiveWindow.Refresh: Application.Refresh
        textShape.Fill.UniformColor.CMYKAssign 15, 100, 100, 0
        textShape.Text.Story.Font = "黑体"
        ActiveWindow.Refresh: Application.Refresh
        
        srt.Add textShape
    Next i
    
    srt.LeftX = ocrsrl + offl
    srt.TopY = ocrsrt - offh
    srt.Group
End Sub
Function URLEncode(DecodeString As String) As String
    Dim i As Long ' 使用 Long 类型来处理较大的索引值
    Dim CharCode As Integer
    Dim Result As String
    Dim ChunkSize As Integer
    Dim StartIndex As Long ' 使用 Long 类型来处理较大的索引值
    Dim EndIndex As Long ' 使用 Long 类型来处理较大的索引值

    ChunkSize = 10 ' 进一步减小每个分段的大小

    For StartIndex = 1 To Len(DecodeString) Step ChunkSize
        EndIndex = StartIndex + ChunkSize - 1
        If EndIndex > Len(DecodeString) Then
            EndIndex = Len(DecodeString)
        End If

        For i = StartIndex To EndIndex
            CharCode = Asc(Mid(DecodeString, i, 1))
            If (CharCode >= 48 And CharCode <= 57) Or _
               (CharCode >= 65 And CharCode <= 90) Or _
               (CharCode >= 97 And CharCode <= 122) Or _
               CharCode = 45 Or CharCode = 46 Or _
               CharCode = 95 Or CharCode = 126 Then
                Result = Result & Chr(CharCode)
            Else
                Result = Result & "%" & Hex(CharCode)
            End If
        Next i
    Next StartIndex

    URLEncode = Result
End Function

Function ConvertImageToBase64(imagePath As String) As String
    Dim b64 As String
    Dim objStream As Object

    Set objStream = CreateObject("ADODB.Stream")
    objStream.Type = 1 ' Binary
    objStream.Open
    objStream.LoadFromFile imagePath

    Dim bytes() As Byte
    bytes = objStream.Read

    b64 = EncodeBase64(bytes)

    ConvertImageToBase64 = b64

    objStream.Close
End Function

Function EncodeBase64(data() As Byte) As String
    Dim xml As Object
    Dim node As Object

    Set xml = CreateObject("MSXML2.DOMDocument")
    Set node = xml.createElement("b64")
    node.DataType = "bin.base64"
    node.nodeTypedValue = data
    EncodeBase64 = node.Text

    EncodeBase64 = Replace(node.Text, vbLf, "")
End Function