Option Explicit

' API函数声明，兼容32位和64位Office
#If VBA7 Then
    Public Declare PtrSafe Sub Sleep Lib "kernel32" (ByVal dwMilliseconds As LongPtr)
    Private Declare PtrSafe Function SetForegroundWindow Lib "user32" (ByVal hWnd As LongPtr) As Long
    Private Declare PtrSafe Function FindWindow Lib "user32" Alias "FindWindowA" (ByVal lpClassName As String, ByVal lpWindowName As String) As LongPtr
    Public Declare PtrSafe Function SetCursorPos Lib "user32" (ByVal x As Long, ByVal y As Long) As Long
    Public Declare PtrSafe Sub mouse_event Lib "user32" (ByVal dwFlags As Long, ByVal dx As Long, ByVal dy As Long, ByVal cButtons As Long, ByVal dwExtraInfo As Long)
#Else
    Public Declare Sub Sleep Lib "kernel32" (ByVal dwMilliseconds As Long)
    Private Declare Function SetForegroundWindow Lib "user32" (ByVal hWnd As Long) As Long
    Private Declare Function FindWindow Lib "user32" Alias "FindWindowA" (ByVal lpClassName As String, ByVal lpWindowName As String) As Long
    Public Declare Function SetCursorPos Lib "user32" (ByVal x As Long, ByVal y As Long) As Long
    Public Declare Sub mouse_event Lib "user32" (ByVal dwFlags As Long, ByVal dx As Long, ByVal dy As Long, ByVal cButtons As Long, ByVal dwExtraInfo As Long)
#End If

Const MOUSEEVENTF_LEFTDOWN = &H2
Const MOUSEEVENTF_LEFTUP = &H4

' 计算对象长宽的自定义函数
Function CalcDimensions(x1 As Double, y1 As Double, x2 As Double, y2 As Double) As Variant
    Dim width As Double
    Dim height As Double
    
    ' 宽度为两个X坐标差的绝对值
    width = Abs(x2 - x1)
    ' 高度为两个Y坐标差的绝对值
    height = Abs(y2 - y1)
    
    ' 返回数组，第一个元素为宽度，第二个元素为高度
    CalcDimensions = Array(width, height)
End Function

' =========================================================================================
' 主程序：读取屏幕坐标，调用函数获取文档坐标，然后调整并移动选中对象
' =========================================================================================
Sub 移动到对应位置()
    Dim shp As Shape
    Dim newWidthMM As Double
    Dim newHeightMM As Double
    Dim fso As Object
    Dim ts As Object
    Dim fileContent As String
    Dim Values() As String
    Dim Dimensions As Variant
    Dim PluginPath As String
    Dim screenX As Long, screenY As Long
    Dim selectedShape As Shape
    Dim result As String
    Dim Valuess() As String
    Dim huabu1 As Double, huabu2 As Double, huabu3 As Double, huabu4 As Double
    
    Dim currentSelection As ShapeRange
    Dim targetShapee As Shape

    ' 检查并获取当前选中的第一个对象
    If ActiveDocument.Selection.Shapes.Count > 0 Then
        Set targetShapee = ActiveDocument.Selection.Shapes(1)
    Else
        MsgBox "请先选择至少一个对象！", vbExclamation
        Exit Sub
    End If
    
    ' 创建FileSystemObject
    Set fso = CreateObject("Scripting.FileSystemObject")
    PluginPath = Application.GMSManager.UserGMSPath
    
    ' 检查坐标文件是否存在
    If Not fso.FileExists(PluginPath & "\orc\坐标信息.txt") Then
        MsgBox "未找到坐标文件：" & PluginPath & "\orc\坐标信息.txt", vbCritical
        Exit Sub
    End If
    
    ' 打开并读取“坐标信息.txt”文件
    Set ts = fso.OpenTextFile(PluginPath & "\orc\坐标信息.txt", 1) ' 1 = ForReading
    fileContent = ts.ReadAll
    ts.Close
    
    Values = Split(fileContent, ",")
    If UBound(Values) < 3 Then
        MsgBox "坐标文件内容格式不正确，应至少包含4个由逗号分隔的数值。", vbCritical
        Exit Sub
    End If

    ' --- 获取第一个点的文档坐标 ---
    screenX = CLng(Values(0))
    screenY = CLng(Values(1))
    
    ' 调用函数将屏幕坐标转换为文档坐标
    result = ClickAndGetPosition(screenX, screenY)
    If InStr(result, "Error") > 0 Then
        MsgBox result, vbCritical
        Exit Sub
    End If
    
    Valuess = Split(result, ",")
    huabu1 = CDbl(Valuess(0))
    huabu2 = CDbl(Valuess(1))
    Sleep 200

    ' --- 获取第二个点的文档坐标 ---
    screenX = CLng(Values(2))
    screenY = CLng(Values(3))
    
    ' 再次调用函数
    result = ClickAndGetPosition(screenX, screenY)
     If InStr(result, "Error") > 0 Then
        MsgBox result, vbCritical
        Exit Sub
    End If
    
    Valuess = Split(result, ",")
    huabu3 = CDbl(Valuess(0))
    huabu4 = CDbl(Valuess(1))
    
    ' --- 计算尺寸并调整对象 ---
    Dimensions = CalcDimensions(huabu1, huabu2, huabu3, huabu4)
   
    ' 指定宽度和高度，单位毫米
    newWidthMM = Dimensions(0)
    newHeightMM = Dimensions(1)
    
    ' 设置单位为毫米，避免单位换算问题
    ActiveDocument.Unit = cdrMillimeter
    targetShapee.CreateSelection ' 确保操作对象是最初选中的那个
    
    ' 调整选中对象的大小
    For Each shp In ActiveDocument.Selection.Shapes
        shp.SetSize newWidthMM, newHeightMM
    Next shp

    ' --- 移动对象到指定位置 ---
    Dim TARGET_X As Double, TARGET_Y As Double
    ' 定位到第一个点的坐标
    TARGET_X = huabu1
    TARGET_Y = huabu2

    ' 如果多选，则先打组进行整体定位
    If ActiveSelection.Shapes.Count > 1 Then
        Set shp = ActiveSelectionRange.Group
    Else
        Set shp = ActiveShape
    End If

    ' 将对象的“左上角”对齐到目标坐标
    shp.SetPositionEx cdrTopLeft, TARGET_X, TARGET_Y

    ' 如果之前打组了，可以取消组合
    If shp.Type = cdrGroupShape And shp.Shapes.Count > 1 Then shp.UngroupAll
    

    
End Sub


' =========================================================================================
' 已修改的函数：接收屏幕坐标，返回CorelDRAW文档坐标字符串
' =========================================================================================
Public Function ClickAndGetPosition(ByVal screenX As Long, ByVal screenY As Long) As String
    Dim originalUnit As cdrUnit
    Dim hwndCorel As LongPtr
    Dim className As String
    Dim i As Integer
    Dim found As Boolean
    Dim docX As Double, docY As Double
    
    On Error GoTo HandleError
    
    found = False
    ' 查找并激活CorelDRAW窗口，以确保坐标转换的上下文正确
    For i = 12 To 28 ' 扩大版本搜索范围
        className = "CorelDRAW" & i
        hwndCorel = FindWindow(className, vbNullString)
        
        If hwndCorel <> 0 Then
            SetForegroundWindow hwndCorel
            found = True
            Exit For
        End If
    Next i
    
    ' 如果未找到CorelDRAW窗口，返回错误信息
    If Not found Then
        ClickAndGetPosition = "Error: CorelDRAW window not found"
        Exit Function
    End If
    
    ' --- 1. 单位设置 ---
    originalUnit = ActiveDocument.Unit
    ActiveDocument.Unit = cdrMillimeter

    ' --- 2. 核心转换 ---
    ' 使用CorelDRAW内置函数将传入的屏幕坐标转换为文档坐标
    ActiveWindow.ScreenToDocument screenX, screenY, docX, docY
    
    ' 检查转换是否成功
    If Err.Number <> 0 Then
        Err.Clear
        ClickAndGetPosition = "Error: Failed to convert screen coordinates to document coordinates."
        GoTo Cleanup
    End If
    
    ' --- 3. 格式化并准备返回值 ---
    ' 将获取到的文档坐标格式化为 "X,Y" 字符串
    ClickAndGetPosition = CStr(docX) & "," & CStr(docY)
    
' --- 4. 清理 ---
Cleanup:
    If originalUnit <> 0 Then
        ActiveDocument.Unit = originalUnit
    End If
    Exit Function

' --- 5. 错误处理 ---
HandleError:
    ClickAndGetPosition = "Error: " & Err.Description
    GoTo Cleanup
End Function



