
Option Explicit
'==============================================================
' API Declarations and Constants
'==============================================================
Dim copiedTextBox As Shape
Dim copiedFont As String
Dim copiedFontSize As Single
' 全局变量，保存复制的文本框
#If VBA7 Then ' For 64-bit and 32-bit VBA7 (Office 2010 and later)
    Private Declare PtrSafe Function FindWindowA Lib "user32" (ByVal lpClassName As String, ByVal lpWindowName As String) As LongPtr
    Private Declare PtrSafe Function GetWindowLongPtrA Lib "user32" (ByVal hWnd As LongPtr, ByVal nIndex As Long) As LongPtr
    Private Declare PtrSafe Function SetWindowLongPtrA Lib "user32" (ByVal hWnd As LongPtr, ByVal nIndex As Long, ByVal dwNewLong As LongPtr) As LongPtr
    Private Const GWL_STYLE As Long = -16
    Private Const WS_MINIMIZEBOX As Long = &H20000 ' 最小化按钮样式
    ' Removed: Private Const WS_MAXIMIZEBOX As Long = &H10000 ' 最大化/还原按钮样式
#Else ' For 32-bit VBA6 and earlier (Office 2007 and earlier)
    Private Declare Function FindWindowA Lib "user32" (ByVal lpClassName As String, ByVal lpWindowName As String) As Long
    Private Declare Function GetWindowLongA Lib "user32" (ByVal hWnd As Long, ByVal nIndex As Long) As Long
    Private Declare Function SetWindowLongA Lib "user32" (ByVal hWnd As Long, ByVal nIndex As Long, ByVal dwNewLong As Long) As Long
    Private Const GWL_STYLE As Long = -16
    Private Const WS_MINIMIZEBOX As Long = &H20000
    ' Removed: Private Const WS_MAXIMIZEBOX As Long = &H10000
#End If

' UserForm Event - Initialize
' This is where we modify the window style
'==============================================================

Private Sub UserForm_Initialize()

    Dim hWndForm As LongPtr ' Use LongPtr for window handle

    ' Find the window handle of this UserForm
    hWndForm = FindWindowA("ThunderDFrame", Me.Caption)

    ' Check if the window handle was found
    If hWndForm <> 0 Then
        Dim currentStyle As LongPtr ' Use LongPtr for window style

        ' Get the current window style
        #If VBA7 Then
            currentStyle = GetWindowLongPtrA(hWndForm, GWL_STYLE)
        #Else
            currentStyle = GetWindowLongA(hWndForm, GWL_STYLE)
        #End If

        ' Add ONLY the minimize box style using bitwise OR
        ' We also remove the maximize box style if it was already present
        Dim newStyle As LongPtr
        ' Keep existing styles AND minimize box, but NOT maximize box
        ' newStyle = (currentStyle Or WS_MINIMIZEBOX) And (Not WS_MAXIMIZEBOX) ' This is one way
        ' A simpler way if we assume the form starts without them is just to add WS_MINIMIZEBOX
        ' If you want to be sure to remove WS_MAXIMIZEBOX if it was there initially (unlikely for UserForm),
        ' use: newStyle = (currentStyle Or WS_MINIMIZEBOX) And (Not WS_MAXIMIZEBOX)
        ' However, standard UserForms don't have WS_MAXIMIZEBOX initially, so simply adding WS_MINIMIZEBOX is sufficient.
        newStyle = currentStyle Or WS_MINIMIZEBOX


        ' Set the new window style
        #If VBA7 Then
            SetWindowLongPtrA hWndForm, GWL_STYLE, newStyle
        #Else
            SetWindowLongA hWndForm, GWL_STYLE, newStyle
        #End If

    Else
        Debug.Print "Could not find the window handle for UserForm: " & Me.Caption
    End If

End Sub

'==============================================================
' Optional: Handle the Close Button (X) behavior
' (Same as before, hides instead of unloads)
'==============================================================

Private Sub UserForm_QueryUnload(Cancel As Integer, UnloadMode As Integer)
    If UnloadMode = vbFormControlMenu Then
        Me.Hide
        Cancel = True
    End If
End Sub



' 在模块的最上方建议加上这句，强制所有变量必须先声明，有助于减少错误
Option Explicit

Private Sub 调用Windows截图工具_Click()
    Dim Wsh As Object
    Dim exePath As String
    Dim currentPath As String
    Dim startTime As Date
    Dim timeoutSeconds As Integer
    Dim processKilled As Boolean

    ' --- 1. 准备工作和路径检查 ---
    On Error GoTo ErrorHandler ' 开启错误处理

    ' 设置超时时间（秒）
    timeoutSeconds = 10
    
    ' 获取当前VBA工程所在的文件路径
    currentPath = Application.GMSManager.UserGMSPath
    
    ' 组装路径
    exePath = currentPath & "\orc\截图.exe"
    
    ' 检查截图程序文件是否存在，如果不存在则提示并退出
    If Dir(exePath) = "" Then
        MsgBox "错误：未在以下路径找到截图程序！" & vbCrLf & exePath, vbCritical, "文件未找到"
        Exit Sub
    End If
    
    ' --- 2. 启动截图程序（非阻塞模式） ---
    Set Wsh = CreateObject("WScript.Shell")
    ' 第三个参数为 False，表示不等待程序执行完成，VBA代码会立即继续向下运行
    Wsh.Run """" & exePath & """", 1, False
    
    ' 记录开始时间
    startTime = Now
    processKilled = False
    
    
    ' --- 3. 循环等待和监控 ---
    Do
        ' 释放CPU资源，避免VBA假死
        DoEvents
        
        ' 检查截图进程是否已经结束
        If Not IsProcessRunning("截图.exe") Then
            ' 如果进程不存在了，说明用户已经截图完成或手动关闭了截图工具
            ' 成功完成，跳出循环
            Exit Do
        End If
        
        ' 检查是否超时
        If DateDiff("s", startTime, Now) >= timeoutSeconds Then
            ' 超过10秒，超时了
            MsgBox "截图操作超时。", vbExclamation, "操作超时"
            
            ' 使用命令行工具 taskkill 强制关闭进程
            ' /F 表示强制终止, /IM 表示通过映像名称（进程名）来识别
            ' 参数 0 表示隐藏命令行窗口
            Wsh.Run "taskkill /F /IM 截图.exe", 0, True
            
            processKilled = True ' 标记进程是被我们杀掉的
            Exit Do ' 跳出循环
        End If
        Debug.Print DateDiff("s", startTime, Now)
        
    Loop
    
    ' --- 4. 根据结果进行后续操作 ---
    If processKilled Then
        ' 如果是因为超时而杀掉的进程，则终止整个VBA程序的运行
        End ' 这会立即停止所有VBA代码的执行
    Else

        ' ...
    End If

Finally:
    ' 释放对象
    Set Wsh = Nothing
    Exit Sub ' 正常退出

ErrorHandler:
    ' 错误处理程序
    MsgBox "发生意外错误：" & vbCrLf & Err.Description, vbCritical, "VBA 错误"
    Set Wsh = Nothing
End Sub


' --- 辅助函数：检查指定的进程是否正在运行 ---
' 使用 WMI (Windows Management Instrumentation) 来查询进程列表
' 参数: processName - 进程的名称，例如 "截图.exe"
' 返回值: True - 进程正在运行, False - 进程未运行
Private Function IsProcessRunning(ByVal processName As String) As Boolean
    Dim objWMIService As Object
    Dim colProcesses As Object
    
    On Error Resume Next ' 如果WMI查询出错，则直接返回False
    
    IsProcessRunning = False
    Set objWMIService = GetObject("winmgmts:\\.\root\cimv2")
    Set colProcesses = objWMIService.ExecQuery("SELECT * FROM Win32_Process WHERE Name = '" & processName & "'")
    
    If colProcesses Is Nothing Then
        IsProcessRunning = False
    ElseIf colProcesses.Count > 0 Then
        IsProcessRunning = True
    End If
    
    ' 清理对象
    Set colProcesses = Nothing
    Set objWMIService = Nothing
    On Error GoTo 0 ' 恢复错误处理
End Function



Sub 截图识别并替换()

   
    Dim DataObj As New MSForms.DataObject
    Dim PlainText As String
    Dim ActiveShape As Shape
    调用Windows截图工具_Click
    ' 获取剪切板内容
    On Error Resume Next
    DataObj.GetFromClipboard
    PlainText = DataObj.GetText
    On Error GoTo 0
    ' 检查剪切板是否有文本
    If PlainText <> "" Then
        ' 检查是否有选中的形状
        If ActiveDocument.Selection.Shapes.Count = 0 Then
            MsgBox "请先选择一个文本框！", vbExclamation, "提示"
            Exit Sub
        Else
            Set ActiveShape = ActiveDocument.Selection.Shapes(1)
            ' 检查选中的形状是否为文本框
            If ActiveShape.Type = cdrTextShape Then
                ' 直接替换文本框中的内容
                ActiveShape.Text.Story.Text = PlainText
            Else
                MsgBox "选中的对象不是文本框，请选择一个文本框！", vbExclamation, "提示"
            End If
        End If
    Else
        MsgBox "识别失败，请重试", vbExclamation, "提示"
    End If
    
     
End Sub
Sub 截图识别并复制()

    Dim DataObj As New MSForms.DataObject
    Dim PlainText As String
    Dim ActiveShape As Shape
    调用Windows截图工具_Click
   
     
End Sub






Private Sub 单行截图复制_Click()
Label6.Caption = "首次启动需等待几秒"
Dim txtPath As String
    Dim PluginPath As String
     Dim fso As Object
    '获取当前宏（插件）所在目录
    PluginPath = Application.GMSManager.UserGMSPath
    '组合成ORC目录路径
    txtPath = PluginPath & "\orc\lx.txt"
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    '创建并写入文件
    With fso.CreateTextFile(txtPath, True)
        .WriteLine "1"
        .Close
    End With
    截图识别并复制

    If fso.FileExists(txtPath) Then
        fso.DeleteFile txtPath
    End If
    
    '释放对象
    Set fso = Nothing
     Label6.Caption = "识别成功"
End Sub

Private Sub 单行截图识别_Click()
    Label6.Caption = "首次启动需等待几秒"
    Dim txtPath As String
    Dim PluginPath As String
     Dim fso As Object
    '获取当前宏（插件）所在目录
    PluginPath = Application.GMSManager.UserGMSPath
    '组合成ORC目录路径
    txtPath = PluginPath & "\orc\lx.txt"
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    Debug.Print txtPath
    
    '创建并写入文件
    With fso.CreateTextFile(txtPath, True)
        .WriteLine "1"
        .Close
    End With
    截图识别并替换

    If fso.FileExists(txtPath) Then
        fso.DeleteFile txtPath
    End If
    
    '释放对象
    Set fso = Nothing
    Label6.Caption = "识别成功"
End Sub

Private Sub 对象至剪切板_Click()
Dim txtPath1 As String
    Dim PluginPath As String
     Dim fso As Object
    '获取当前宏（插件）所在目录
    PluginPath = Application.GMSManager.UserGMSPath
    '组合成ORC目录路径
    txtPath1 = PluginPath & "\orc\lx.txt"
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    '创建并写入文件
    With fso.CreateTextFile(txtPath1, True)
        .WriteLine "4"
        .Close
    End With
    Dim objSelection As ShapeRange
    Dim objShape As Shape
    Dim objWidth As Double
    Dim objHeight As Double
    Dim tempPath As String
    Dim exportFileName As String
    Dim exportCount As Integer

    ' 获取窗体TextBox2中的值
    On Error Resume Next
    exportCount = CInt(TextBox2.Text)
    On Error GoTo 0

    If exportCount <= 0 Then
        MsgBox "请输入有效的数字！使用默认值9。"
        exportCount = 9
    End If

    ' 定义导出路径
    tempPath = Application.GMSManager.UserGMSPath & "orc\tp\"

    ' 清空导出路径中的所有文件
    Dim fs As Object
    Set fs = CreateObject("Scripting.FileSystemObject")
    If fs.FolderExists(tempPath) Then
        fs.DeleteFile tempPath & "*.*", True
    Else
        fs.CreateFolder tempPath
    End If

    ' 获取当前选中的对象
    Set objSelection = ActiveSelectionRange

    ' 检查选中对象的数量
    If objSelection.Count = 0 Then
        MsgBox "未选中任何对象！"
        Exit Sub
    ElseIf objSelection.Count > exportCount Then
        MsgBox "选中对象不能超过 " & exportCount & " 个！"
        Exit Sub
    End If

    ' 最大尺寸（mm）
    Dim maxWidth_mm As Double: maxWidth_mm = 300
    Dim maxHeight_mm As Double: maxHeight_mm = 300

    ' 转换为像素
    Dim maxWidth_px As Double: maxWidth_px = (maxWidth_mm * exportCount) / 25.4
    Dim maxHeight_px As Double: maxHeight_px = (maxHeight_mm * exportCount) / 25.4

    Dim scaleFactor As Double
    Dim resizedWidth As Double
    Dim resizedHeight As Double

    Dim i As Integer: i = 1
    For Each objShape In objSelection
        ' 获取每个对象尺寸 (mm转px)
        objWidth = (objShape.SizeWidth * 25.4 / ActiveDocument.Unit) * exportCount / 25.4
        objHeight = (objShape.SizeHeight * 25.4 / ActiveDocument.Unit) * exportCount / 25.4

        ' 计算缩放比例
        If maxWidth_px / objWidth < maxHeight_px / objHeight Then
            scaleFactor = maxWidth_px / objWidth
        Else
            scaleFactor = maxHeight_px / objHeight
        End If

        resizedWidth = objWidth * scaleFactor
        resizedHeight = objHeight * scaleFactor

        ' 设置文件名
        exportFileName = tempPath & CStr(i) & ".jpg"

        ' 导出图片
        objShape.CreateSelection
        Dim expflt As ExportFilter
        Set expflt = ActiveDocument.ExportBitmap(exportFileName, cdrJPEG, cdrSelection, cdrRGBColorImage, resizedWidth, resizedHeight, exportCount, exportCount, cdrNormalAntiAliasing, False, False, True, False, cdrCompressionNone)
        expflt.Finish

        i = i + 1
    Next objShape

   调用Windows截图工具_Click

End Sub


Private Sub 多行截图复制_Click()
Label6.Caption = "首次启动需等待几秒"
Dim txtPath As String
    Dim PluginPath As String
     Dim fso As Object
    '获取当前宏（插件）所在目录
    PluginPath = Application.GMSManager.UserGMSPath
    '组合成ORC目录路径
    txtPath = PluginPath & "\orc\lx.txt"
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    '创建并写入文件
    With fso.CreateTextFile(txtPath, True)
        .WriteLine "2"
        .Close
    End With
    截图识别并复制

    If fso.FileExists(txtPath) Then
        fso.DeleteFile txtPath
    End If
    
    '释放对象
    Set fso = Nothing
     Label6.Caption = "识别成功"
End Sub

Private Sub 多行截图识别_Click()
Label6.Caption = "首次启动需等待几秒"
Dim txtPath As String
    Dim PluginPath As String
     Dim fso As Object
    '获取当前宏（插件）所在目录
    PluginPath = Application.GMSManager.UserGMSPath
    '组合成ORC目录路径
    txtPath = PluginPath & "\orc\lx.txt"
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    '创建并写入文件
    With fso.CreateTextFile(txtPath, True)
        .WriteLine "2"
        .Close
    End With
    截图识别并替换

    If fso.FileExists(txtPath) Then
        fso.DeleteFile txtPath
    End If
    
    '释放对象
    Set fso = Nothing
    Label6.Caption = "识别成功"
End Sub





Private Sub 去文本格式替换_Click()
 Dim DataObj As New MSForms.DataObject
    Dim PlainText As String
    Dim ActiveShape As Shape
    
    ' 获取剪切板内容
    On Error Resume Next
    DataObj.GetFromClipboard
    PlainText = DataObj.GetText
    On Error GoTo 0
    
    ' 检查剪切板是否有文本
    If PlainText <> "" Then
        ' 检查是否有选中的形状
        If ActiveDocument.Selection.Shapes.Count = 0 Then
            MsgBox "请先选择一个文本框！", vbExclamation, "提示"
            Exit Sub
        Else
            Set ActiveShape = ActiveDocument.Selection.Shapes(1)
            ' 检查选中的形状是否为文本框
            If ActiveShape.Type = cdrTextShape Then
                ' 直接替换文本框中的内容
                ActiveShape.Text.Story.Text = PlainText
            Else
                MsgBox "选中的对象不是文本框，请选择一个文本框！", vbExclamation, "提示"
            End If
        End If
    Else
        MsgBox "剪切板中没有文本内容！", vbExclamation, "提示"
    End If
End Sub

Private Sub 去文本格式粘贴_Click()
 Dim DataObj As New MSForms.DataObject
    Dim PlainText As String
    Dim ActiveShape As Shape
    Dim Clipboard As Object
    Set Clipboard = CreateObject("new:{1C3B4210-F441-11CE-B9EA-00AA006B1A69}")
    ' 获取剪切板内容
    On Error Resume Next
    DataObj.GetFromClipboard
    PlainText = DataObj.GetText
    Clipboard.SetText PlainText
    Clipboard.PutInClipboard
    Set Clipboard = Nothing
    On Error GoTo 0
    
    ' 检查剪切板是否有文本
    If PlainText <> "" Then
        ' 检查是否有选中的形状
        If ActiveDocument.Selection.Shapes.Count = 0 Then
            MsgBox "请先选择一个文本框！", vbExclamation, "提示"
            Exit Sub
        Else
            Set ActiveShape = ActiveDocument.Selection.Shapes(1)
            ' 检查选中的形状是否为文本框
            If ActiveShape.Type = cdrTextShape Then
                ActiveLayer.Paste
            Else
                MsgBox "选中的对象不是文本框，请选择一个文本框！", vbExclamation, "提示"
            End If
        End If
    Else
        MsgBox "剪切板中没有文本内容！", vbExclamation, "提示"
    End If
End Sub

Private Sub 设置间距_Click()
Label6.Caption = "正在执行，请稍后……"
   Dim textBoxValue As String
    textBoxValue = TextBox1.Text ' 获取TextBox1的值
    SetSelectedTextSpacing100 (textBoxValue)
    Label6.Caption = "执行完成"
End Sub
Private Sub 复制按钮_Click()

Label6.Caption = "属性已复制，请选中对象点击粘贴属性"
    Dim sr As ShapeRange
    Set sr = ActiveSelectionRange
    Dim s As Shape
    For Each s In ActiveSelection.Shapes
        If s.Type = cdrTextShape Then
            With s.Text.Story.Characters(1)
           Set copiedTextBox = sr.Shapes(1)
                copiedFont = .font
                 Debug.Print copiedFont
        Debug.Print copiedFontSize
            End With
            Exit For '取到第一个文本对象的属性即退出
            Else
        MsgBox "请选中一个文本框进行复制！", vbExclamation
        End If
    Next

    

End Sub

Private Sub 识别并还原_Click()
Label6.Caption = "正在执行，截图完成后请勿移动鼠标……"
Dim txtPath As String
    Dim PluginPath As String
     Dim fso As Object
     
    '获取当前宏（插件）所在目录
    PluginPath = Application.GMSManager.UserGMSPath
    '组合成ORC目录路径
    txtPath = PluginPath & "\orc\lx.txt"
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    Debug.Print txtPath
    
    
    '创建并写入文件
    With fso.CreateTextFile(txtPath, True)
        .WriteLine "3"
        .Close
    End With
     截图识别并复制
     RunProcessingAndInsertion
 移动到对应位置
DeleteRectanglesInSelection
    If fso.FileExists(txtPath) Then
        fso.DeleteFile txtPath
    End If
    
    '释放对象
    Set fso = Nothing

Label6.Caption = "执行完成"

End Sub

Private Sub 统一中英文间距_Click()
Label6.Caption = "正在执行，请稍后……"
自动获取间距并统一
Label6.Caption = "执行完成"
End Sub

' 粘贴按钮事件：把复制的属性粘贴到选中的文本框上，同时保留目标文本内容
Private Sub 粘贴按钮_Click()
    Dim pastedShape As Shape
    Dim selShape As Shape
    Dim textContent As String
    Dim targetPositionX As Double
    Dim targetPositionY As Double

    ' 检查是否已经复制了文本框
    If copiedTextBox Is Nothing Then
        MsgBox "请先复制文本框！", vbExclamation
        Exit Sub
    End If

    ' 检查是否有目标文本框选中
    If ActiveSelection.Shapes.Count = 0 Then
        MsgBox "请至少选中一个目标文本框进行粘贴！", vbExclamation
        Exit Sub
    End If

    ' 遍历所有选中的文本框
    For Each selShape In ActiveSelection.Shapes
        If selShape.Type = cdrTextShape Then
            ' 保留目标文本内容
            textContent = selShape.Text.Story.Text
            ' 记录目标文本框的位置
            targetPositionX = selShape.PositionX
            targetPositionY = selShape.PositionY

            ' 删除目标文本框
            selShape.Delete

            ' 粘贴复制的文本框到原位置
            Set pastedShape = copiedTextBox.Duplicate
            pastedShape.PositionX = targetPositionX
            pastedShape.PositionY = targetPositionY

            ' 恢复目标内容
            pastedShape.Text.Story.Text = textContent

            ' 恢复字体和字号
            Debug.Print copiedFont
            
            pastedShape.Text.Story.font = copiedFont
        End If
    Next selShape
End Sub

Private Sub 执行统一_Click()
Label6.Caption = "正在执行，请稍后……"
    Dim s As Shape
    Dim defaultFont As String
    Dim defaultSize As Double
    Dim defaultFillColor As Color
    Dim defaultOutlineColor As Color
    Dim defaultOutlineWidth As Double
    Dim hasOutline As Boolean
    Dim i As Long

    ' 确保选择了文本对象
    If ActiveSelection.Shapes.Count = 0 Then
        MsgBox "请先选择文本对象！", vbExclamation
        Exit Sub
    End If

    ' 取第一个文本对象的属性作为默认值
    For Each s In ActiveSelection.Shapes
        If s.Type = cdrTextShape Then
            With s.Text.Story.Characters(1)
                defaultFont = .font
                defaultSize = .Size
                Set defaultFillColor = .Fill.UniformColor

                hasOutline = (.Outline.width > 0)
                If hasOutline Then
                    Set defaultOutlineColor = .Outline.Color
                    defaultOutlineWidth = .Outline.width
                End If
            End With
            Exit For '取到第一个文本对象的属性即退出
        End If
    Next

    ' 遍历所有选中的文本对象，逐字符统一属性
    For Each s In ActiveSelection.Shapes
        If s.Type = cdrTextShape Then
            For i = 1 To s.Text.Story.Characters.Count
                With s.Text.Story.Characters(i)
                    If CheckBox1.Value Then
                        .font = defaultFont
                    End If

                    If CheckBox2.Value Then
                        .Size = defaultSize
                    End If

                    If CheckBox3.Value Then
                        .Fill.UniformColor = defaultFillColor
                    End If

                    If CheckBox4.Value And hasOutline Then
                        .Outline.width = defaultOutlineWidth
                        .Outline.Color.CopyAssign defaultOutlineColor
                    End If
                End With
            Next i
        End If
    Next

    ' 显示完成信息
    Dim msg As String
    msg = "文本属性统一操作已完成："

    If CheckBox1.Value Then
        msg = msg & vbCrLf & "- 字体：" & defaultFont
    End If

    If CheckBox2.Value Then
        msg = msg & vbCrLf & "- 字号：" & defaultSize
    End If

    If CheckBox3.Value Then
        msg = msg & vbCrLf & "- 填充颜色：" & defaultFillColor.Name
    End If

    If CheckBox4.Value Then
        If hasOutline Then
            msg = msg & vbCrLf & "- 轮廓颜色：" & defaultOutlineColor.Name & vbCrLf & "- 轮廓宽度：" & defaultOutlineWidth
        Else
            msg = msg & vbCrLf & "- 无轮廓，不执行轮廓统一操作"
        End If
    End If

    MsgBox msg, vbInformation
Label6.Caption = "执行完成"
End Sub


'-----------------------------------------------------------------------------
' 主过程: 读取数据文件，处理数据，然后在CorelDRAW中插入文本图形
'-----------------------------------------------------------------------------
Sub RunProcessingAndInsertion()

    Dim processedDataCollection As Collection ' 用于临时存储处理后的数据
    Dim dataForInsertion() As Variant       ' 转换成Variant数组，用于传递给插入函数

    Debug.Print "--- 开始处理并插入数据 ---"

    ' 1. 调用函数读取并格式化数据
    Set processedDataCollection = ReadAndFormatData()

    ' 检查是否成功获取到数据
    If processedDataCollection Is Nothing Or processedDataCollection.Count = 0 Then
        Debug.Print "未能获取到有效数据或数据处理出错，终止操作。"
        Exit Sub
    End If

    Debug.Print "--- 数据读取和格式化完成，共找到 " & processedDataCollection.Count & " 条数据 ---"

    ' 2. 将Collection转换为Variant数组
    ' Collection是1-based索引，Variant数组通常是0-based，这里创建0-based数组
    ReDim dataForInsertion(0 To processedDataCollection.Count - 1)
    Dim i As Long
    For i = 0 To processedDataCollection.Count - 1
        ' 从Collection中取出数据（Collection的Item是1-based）
        dataForInsertion(i) = processedDataCollection.Item(i + 1)
    Next i

    ' 3. 调用 InsertTextShapes 宏在文档中插入图形
    Debug.Print "--- 调用 InsertTextShapes 插入图形 ---"
    InsertTextShapes dataForInsertion

    Debug.Print "--- 所有操作完成 ---"

End Sub


'-----------------------------------------------------------------------------
' Function: ReadAndFormatData
' Purpose: Reads data from a text file, parses it, calculates dimensions,
'          and returns a Collection of data points suitable for insertion.
'          Handles potential text encoding issues using ADODB.Stream.
'          Refactored from the original ProcessTextData sub.
' Returns: A Collection where each item is a Variant array:
'          [0] As String: The text content (words)
'          [1] As Double: The original top Y coordinate (from file)
'          [2] As Double: The original left X coordinate (from file)
'          [3] As Double: The calculated width (RightX - LeftX)
'          [4] As Double: The calculated height (BottomY - TopY)
'          Returns Nothing if a file reading error occurs.
'-----------------------------------------------------------------------------
Function ReadAndFormatData() As Collection

    ' ADODB.Stream requires a reference (Tools -> References -> Microsoft ActiveX Data Objects x.x Library)
    ' Or use late binding with CreateObject as shown below. Late binding is used here.

    Dim filePath As String          ' Full path to the text file
    Dim fileContent As String       ' Entire content of the file
    Dim dataGroups() As String      ' Data groups after splitting by "~"
    Dim dataParts() As String       ' Parts of a data group after splitting by "|#|"
    Dim numbers() As String         ' Individual number strings from the second part
    Dim objStream As Object         ' ADODB.Stream object
    Dim resultsCollection As New Collection ' Collection to hold formatted data arrays
    Dim i As Long                   ' Loop counter

    ' --- Error Handling ---
    ' Enable error handling for file operations within this function
    On Error GoTo FileErrorHandler

    ' --- Configuration ---
    ' Set the full path to your text file. Adjust this path as needed.
    Const FILENAME As String = "内容.txt"
    ' Using Application.GMSManager.UserGMSPath points to the user's GMS folder
    ' Example: C:\Users\<USER>\AppData\Roaming\Corel\CorelDRAW Graphics Suite 202x\GMS\内容.txt
    filePath = Application.GMSManager.UserGMSPath & "\orc\" & FILENAME
    ' Alternatively, if the file is in a specific project subfolder:
    ' filePath = Application.GMSPath & "\YourProjectName\" & FILENAME

    ' --- Read File Content using ADODB.Stream ---
    Set objStream = CreateObject("ADODB.Stream") ' Using late binding
    objStream.Type = 2 ' adTypeText constant (value is 2)
    ' Set the character set (encoding) of the file. Common options: "utf-8", "ascii" (for ANSI), "gb2312", "gbk"
    objStream.CharSet = "utf-8" ' Assuming UTF-8 encoding for the file

    objStream.Open
    objStream.LoadFromFile filePath
    fileContent = objStream.ReadText
    objStream.Close
    Set objStream = Nothing ' Release the object

    ' --- Process the Read Data ---

    ' Check if the file content is empty
    If Trim(fileContent) = "" Then
        Debug.Print "文件内容为空或只包含空白字符。"
        Set ReadAndFormatData = resultsCollection ' Return empty collection
        Exit Function
    End If

    ' Split by group delimiter "~"
    dataGroups = Split(fileContent, "~#")

    ' Check if any groups were found
    If UBound(dataGroups) < LBound(dataGroups) Then
        Debug.Print "未使用 '~' 作为分隔符找到数据组。"
        Set ReadAndFormatData = resultsCollection ' Return empty collection
        Exit Function
    End If

    ' Loop through each data group
    For i = LBound(dataGroups) To UBound(dataGroups)
        Dim currentGroup As String
        currentGroup = Trim(dataGroups(i)) ' Trim whitespace

        ' Skip empty strings resulting from splitting (e.g., trailing "~")
        If currentGroup = "" Then
            GoTo NextGroup_ReadData ' Skip to the next iteration
        End If

        ' Split the group by item delimiter "|#|". Expecting 4 parts.
        dataParts = Split(currentGroup, "|#|")

        ' Check if the split resulted in exactly 4 parts
        If UBound(dataParts) - LBound(dataParts) + 1 = 4 Then
            Dim string1 As String   ' Text part
            Dim string2 As String   ' Comma-separated numbers part

            string1 = dataParts(0)
            string2 = dataParts(1) ' The numbers part

            ' Split the numbers part by ","
            numbers = Split(string2, ",")

            ' Check if there are enough numbers. We need indices 0, 1, 6, 9,
            ' which requires at least 10 elements (0 to 9).
            If UBound(numbers) - LBound(numbers) + 1 >= 10 Then
                Dim num0Str As String: num0Str = Trim(numbers(0)) ' LeftX
                Dim num1Str As String: num1Str = Trim(numbers(1)) ' TopY
                Dim num6Str As String: num6Str = Trim(numbers(6)) ' RightX
                Dim num9Str As String: num9Str = Trim(numbers(9)) ' BottomY

                ' Validate if the required parts are numeric
                If IsNumeric(num0Str) And IsNumeric(num1Str) And IsNumeric(num6Str) And IsNumeric(num9Str) Then
                    Dim leftX As Double: leftX = CDbl(num0Str)
                    Dim topY As Double: topY = CDbl(num1Str)
                    Dim rightX As Double: rightX = CDbl(num6Str)
                    Dim bottomY As Double: bottomY = CDbl(num9Str)

                    ' Calculate width and height
                    Dim calculatedWidth As Double: calculatedWidth = rightX - leftX
                    Dim calculatedHeight As Double: calculatedHeight = bottomY - topY

                     ' --- IMPORTANT ---
                     ' Ensure width/height are non-negative. If coordinates might be reversed,
                     ' use Abs(). Based on LeftX/TopY and RightX/BottomY structure, they *should*
                     ' be positive if BottomY > TopY and RightX > LeftX. We'll assume this for now.
                     ' If calculatedWidth < 0 Then calculatedWidth = Abs(calculatedWidth)
                     ' If calculatedHeight < 0 Then calculatedHeight = Abs(calculatedHeight)


                    ' Create a Variant array for this data point
                    Dim itemData(0 To 4) As Variant
                    itemData(0) = string1         ' Text content (words)
                    itemData(1) = topY            ' Original Top Y coordinate (for 'top' in InsertTextShapes)
                    itemData(2) = leftX           ' Original Left X coordinate (for 'left' in InsertTextShapes)
                    itemData(3) = calculatedWidth ' Calculated Width
                    itemData(4) = calculatedHeight ' Calculated Height

                    ' Add the formatted data array to the collection
                    resultsCollection.Add itemData

                Else
                    ' Warning for non-numeric coordinate values
                    Debug.Print "警告: 跳过数据组 '" & currentGroup & "'，原因：坐标值非数字。"
                End If
            Else
                ' Warning for insufficient numbers
                Debug.Print "警告: 跳过数据组 '" & currentGroup & "'，原因：数字部分不足10个值。"
            End If
        Else
            ' Warning for incorrect format (not 4 parts)
            Debug.Print "警告: 跳过数据组 '" & currentGroup & "'，原因：格式不符，未按 '|#|' 分割成4部分。"
        End If

NextGroup_ReadData: ' Label for GoTo
    Next i

    ' Return the collection containing all successfully processed data points
    Set ReadAndFormatData = resultsCollection
    Exit Function ' Exit function successfully

' --- Error Handling Section ---
FileErrorHandler:
    ' This section is executed if an error occurs during file reading/processing

    Dim errorMessage As String
    errorMessage = "处理数据文件时发生错误:" & vbCrLf & _
                   "源: " & Err.Source & vbCrLf & _
                   "错误号: " & Err.Number & vbCrLf & _
                   "描述: " & Err.Description & vbCrLf & vbCrLf & _
                   "无法处理文件: '" & filePath & "'" & vbCrLf & _
                   "请检查:" & vbCrLf & _
                   "- 文件是否存在于指定路径。" & vbCrLf & _
                   "- 您是否有读取该文件的权限。" & vbCrLf & _
                   "- 文件编码是否正确 ('" & objStream.CharSet & "')。您可能需要修改 objStream.Charset。" & vbCrLf & _
                   "- 文件内容的格式是否与期望一致。"

    MsgBox errorMessage, vbCritical, "数据文件处理错误"

    ' Clean up the ADODB.Stream object in case it was opened
    If Not objStream Is Nothing Then
        If objStream.State = 1 Then ' adStateOpen
            objStream.Close
        End If
        Set objStream = Nothing
    End If

    ' Return Nothing to indicate failure
    Set ReadAndFormatData = Nothing

    ' It's good practice to clear the error state after handling it
    ' On Error GoTo 0 ' Could add this here if desired, but Exit Function also helps clear it.

End Function


'-----------------------------------------------------------------------------
' Sub: InsertTextShapes
' Purpose: Creates artistic text shapes in CorelDRAW based on the provided
'          data array. This sub expects data in a specific format.
'          (Your original sub, with added input checks and comments)

Sub InsertTextShapes(ByVal ocrResults As Variant) ' Use ByVal as the array is not modified

    ' Expected Input Format for ocrResults:
    ' A Variant array (e.g., 0-based) where each element is itself a Variant array (e.g., 0-based) with 5 elements:
    ' [0] As String: Text content (words)
    ' [1] As Double: Original Top Y coordinate (from file, before scaling)
    ' [2] As Double: Original Left X coordinate (from file, before scaling)
    ' [3] As Double: Calculated Width (before scaling)
    ' [4] As Double: Calculated Height (before scaling)

    ' Basic input validation
    If IsEmpty(ocrResults) Then
         Debug.Print "InsertTextShapes: 接收到的数据为空，无需插入。"
         Exit Sub
    End If

    If Not IsArray(ocrResults) Then
         Debug.Print "InsertTextShapes: 接收到的数据不是一个数组，终止操作。"
         Exit Sub
    End If

    ' Ensure there is an active document
    If ActiveDocument Is Nothing Then
        Debug.Print "InsertTextShapes: 没有打开的文档，终止操作。"
        MsgBox "请先打开一个CorelDRAW文档。", vbExclamation, "无活动文档"
        Exit Sub
    End If

    ' --- 添加错误处理 ---
    ' 跳转到 ErrorHandler_InsertTextShapes 标签，以确保 EndCommandGroup 在发生错误时被调用
    On Error GoTo ErrorHandler_InsertTextShapes

    ' Set units for consistency (though CorelDRAW handles internal units well)
    ' ActiveDocument.Unit = cdrMillimeter ' 设置单位为毫米 (如果需要在宏内部强制设置，可以保留)

    Dim textShapes As New ShapeRange ' 用于收集创建的图形以便后续群组
    Dim pageHeight As Double
    ' 获取当前页面的高度，用于转换Y坐标（CorelDRAW使用底部为0的坐标系）
    pageHeight = ActivePage.SizeHeight

    ' 优化：提前设置字体和颜色，避免每次循环中重复设置
    Dim fontName As String: fontName = "微软雅黑" ' 可以根据需要修改字体名称
    Dim textColor As New Color
    textColor.RGBAssign 255, 0, 0 ' 设置字体颜色为红色 (R, G, B)

    ' 原始数据到CorelDRAW单位的比例因子
    Const SCALE_FACTOR As Double = 0.086

    ' --- 优化：开始一个命令组，抑制屏幕更新并提高性能 ---
    ' 这告诉CorelDRAW将接下来的操作视为一个整体
    ActiveDocument.BeginCommandGroup "Insert Text Shapes" ' 可以给命令组一个描述性的名称

    ' ***************************************************************
    ' ---               新增代码开始 (根据您的要求)               ---
    ' ***************************************************************

    ' 1. 声明用于接收宽高的变量
    Dim myKuan As Double
    Dim myGao As Double
    
    ' 2. 调用外部子过程来获取宽高值
    '    请确保名为 `坐标获取_ByRef` 的子过程存在于您的项目中
    坐标获取_ByRef myKuan, myGao
    
    ' 3. 在立即窗口打印获取到的值，用于调试
    Debug.Print "获取到的宽度是: " & myKuan & vbCrLf & "获取到的高度是: " & myGao
    
    ' 4. 在画布内添加一个矩形
    '    检查获取到的宽高是否为有效值（大于0）
    If myKuan > 0 And myGao > 0 Then
        Dim rect As Shape
        ' 使用 CreateRectangle2(LeftX, TopY, Width, Height) 创建矩形
        ' CorelDRAW的Y轴原点在页面底部，所以页面的左上角是 (0, pageHeight)
        Set rect = ActiveLayer.CreateRectangle2(0, pageHeight, myKuan * SCALE_FACTOR, myGao * SCALE_FACTOR)
        
        ' (可选) 再次确认位置，确保其左上角精确对齐
        rect.leftX = 0
        rect.topY = pageHeight
        
        ' ===============================================================
        ' ---                  【核心修改点】                         ---
        ' --- 将新创建的矩形添加到 textShapes 集合中，以便和文本一起群组 ---
        ' ===============================================================
        textShapes.Add rect
        
    Else
        Debug.Print "警告：从 坐标获取_ByRef 获取的宽高无效 (" & myKuan & ", " & myGao & ")，未创建矩形。"
    End If

    ' ***************************************************************
    ' ---               新增代码结束                              ---
    ' ***************************************************************


    ' Loop through the data array provided by the caller
    Dim i As Long
    ' 使用 LBound 和 UBound 确保适应不同维度的数组
    For i = LBound(ocrResults) To UBound(ocrResults)
        Dim currentItemData As Variant ' 当前循环的数据项，期望是一个数组

        ' 验证当前数据项是否是有效的数组且包含足够的元素
        ' 期望数组包含至少 5 个元素 (0 to 4)
        If IsArray(ocrResults(i)) And UBound(ocrResults(i)) - LBound(ocrResults(i)) + 1 >= 5 Then
            ' 使用 On Error Resume Next 仅针对类型转换错误，避免整个循环因一个错误项中断
            On Error Resume Next
            currentItemData = ocrResults(i)

            Dim words As String
            Dim originalTop As Double
            Dim originalLeft As Double
            Dim originalWidth As Double
            Dim originalHeight As Double

            ' 从数据项中提取原始值并尝试转换为正确类型
            words = CStr(currentItemData(0))       ' Text content
            originalTop = CDbl(currentItemData(1)) ' Original Y (Top)
            originalLeft = CDbl(currentItemData(2)) ' Original X (Left)
            originalWidth = CDbl(currentItemData(3)) ' Calculated Width
            originalHeight = CDbl(currentItemData(4)) ' Calculated Height
            ' 关闭临时错误处理
            On Error GoTo ErrorHandler_InsertTextShapes ' 恢复到主错误处理

            ' 检查转换是否失败（Err.Number <> 0）或值是否不合理
            If Err.Number <> 0 Or originalWidth < 0 Or originalHeight < 0 Or words = "" Then
                 ' 重置 Err 对象
                 Err.Clear
                 Debug.Print "警告: 跳过无效、尺寸为负或转换失败的数据项 at index " & i & ": " & words & ", " & originalLeft & ", " & originalTop & ", " & originalWidth & ", " & originalHeight
                 GoTo NextItem ' 跳到下一个循环项
            End If

            ' 应用比例因子到所有尺寸
            Dim scaledTop As Double: scaledTop = originalTop * SCALE_FACTOR
            Dim scaledLeft As Double: scaledLeft = originalLeft * SCALE_FACTOR
            Dim scaledWidth As Double: scaledWidth = originalWidth * SCALE_FACTOR
            Dim scaledHeight As Double: scaledHeight = originalHeight * SCALE_FACTOR

            ' CorelDRAW 的 CreateArtisticText(X, Y, Text) 方法
            ' 这里的 (X, Y) 是文本的初始位置，通常不是左上角
            ' CorelDRAW 的 Y 轴原点在页面底部，需要转换 Y 坐标
            Dim s As Shape
            Set s = ActiveLayer.CreateArtisticText(scaledLeft, pageHeight - scaledTop, words)

            ' 设置文本属性 (字体和颜色)
            ' 检查图形对象是否成功创建
            If Not s Is Nothing Then
                s.Text.Story.Fill.UniformColor = textColor ' 设置字体颜色
                ' 尝试设置字体，字体名称可能需要精确匹配系统中的字体
                On Error Resume Next ' 忽略字体设置错误（如字体不存在）
                s.Text.Story.font = fontName
                On Error GoTo ErrorHandler_InsertTextShapes ' 恢复主错误处理
                If CheckBox5.Value Then
                        Dim targetWidth As Double
                            targetWidth = scaledWidth
    
                        ' 获取当前对象的宽度和高度
                        Dim originalWidth1 As Double
                        Dim originalHeight1 As Double
                        originalWidth1 = s.SizeWidth
                        originalHeight1 = s.SizeHeight
                        
                        ' 计算缩放比例
                        Dim scaleFactor As Double
                        scaleFactor = targetWidth / originalWidth1
                        
                        ' 设置新的宽度和高度（高度等比例缩放）
                        s.SetSize targetWidth, originalHeight1 * scaleFactor
                Else
                        s.SizeWidth = scaledWidth
                         s.SizeHeight = scaledHeight
                End If

                ' 设置图形的尺寸
                ' 再次设置图形的位置，确保其左上角与计算出的位置对齐
                ' 这通常在设置尺寸后进行
                s.leftX = scaledLeft
                s.topY = pageHeight - scaledTop ' 调整 Y 坐标

                ' 将新创建的文本图形添加到 ShapeRange 集合中，以便后续群组
                textShapes.Add s
            Else
                 Debug.Print "警告: 无法为数据项 at index " & i & " 创建图形: " & words
            End If


        Else
             ' 警告格式不符的数据项
             Debug.Print "警告: 跳过格式不符的数据项 at index " & i
        End If

NextItem: ' 跳到下一个循环项的标签
    Next i ' 处理下一个数据项

    ' --- 优化：结束命令组 ---
    ' 在这里结束命令组，使所有更改一次性应用并更新屏幕
    ActiveDocument.EndCommandGroup

    ' 在命令组结束后进行群组操作
    If textShapes.Count > 0 Then
        Dim groupedShape As Shape
        Set groupedShape = textShapes.Group
        groupedShape.CreateSelection
    End If

    ' --- 正常退出子程序 ---
    Exit Sub

' --- 错误处理标签 ---
ErrorHandler_InsertTextShapes:
    ' 在这里处理发生的错误
    MsgBox "在 InsertTextShapes 宏执行过程中发生错误:" & vbCrLf & _
           "错误号: " & Err.Number & vbCrLf & _
           "描述: " & Err.Description & vbCrLf & vbCrLf & _
           "请检查您的数据格式或CorelDRAW状态。", vbCritical, "运行时错误"

    ' --- 确保结束命令组 ---
    ' 即使发生错误，也尝试结束命令组，以避免CorelDRAW状态异常
    ' 检查是否在 BeginCommandGroup 之后发生了错误
    If Err.Number <> 0 Then ' Only try to end if an error occurred
        On Error Resume Next ' 忽略结束命令组时可能发生的错误
        ActiveDocument.EndCommandGroup
        On Error GoTo 0 ' 关闭错误忽略
    End If

    ' 清理 Err 对象
    Err.Clear

    ' 退出子程序
    Exit Sub

End Sub



