Option Explicit

Private Const LINE_NAME As String = "生成线"
Private Const LENGTH_ADJUST_PERCENT As Double = 0.05

Sub 绘制对象连接线()
    On Error GoTo ErrorHandler
    
    Dim shapes As ShapeRange
    If ActiveSelectionRange.Count > 0 Then
        Set shapes = ActiveSelectionRange
    Else
        Set shapes = ActivePage.Shapes.All
    End If
    
    If shapes.Count < 2 Then
        MsgBox "需要至少2个对象才能绘制连接线！", vbExclamation
        Exit Sub
    End If

    ActiveDocument.BeginCommandGroup "绘制对象连接线"
    
    Dim generatedLines As New ShapeRange
    Dim processed() As Boolean
    ReDim processed(1 To shapes.Count)
    Dim tolerance As Double
    tolerance = 计算简单容差(shapes)
    
    Dim i As Integer, j As Integer, k As Integer, m As Integer, n As Integer
    Dim groupIndices As Collection
    Dim baseY As Double
    Dim sortedIndices() As Integer
    Dim temp As Integer
    Dim line As Shape

    For i = 1 To shapes.Count
        If Not processed(i) Then
            Set groupIndices = New Collection
            baseY = shapes(i).CenterY
            groupIndices.Add i
            processed(i) = True

            For j = 1 To shapes.Count
                If j <> i And Not processed(j) Then
                    If Abs(shapes(j).CenterY - baseY) <= tolerance Then
                        groupIndices.Add j
                        processed(j) = True
                    End If
                End If
            Next j

            If groupIndices.Count >= 2 Then
                ReDim sortedIndices(1 To groupIndices.Count)
                For k = 1 To groupIndices.Count
                    sortedIndices(k) = groupIndices(k)
                Next k

                For m = 1 To groupIndices.Count - 1
                    For n = m + 1 To groupIndices.Count
                        If shapes(sortedIndices(m)).CenterX > shapes(sortedIndices(n)).CenterX Then
                            temp = sortedIndices(m)
                            sortedIndices(m) = sortedIndices(n)
                            sortedIndices(n) = temp
                        End If
                    Next n
                Next m

                For k = 1 To groupIndices.Count - 1
                    Set line = 创建连接线(shapes(sortedIndices(k)), shapes(sortedIndices(k + 1)))
                    If Not line Is Nothing Then
                        generatedLines.Add line
                    End If
                Next k
            End If
        End If
    Next i

    If generatedLines.Count > 0 Then
        generatedLines.CreateSelection
    End If

    ActiveDocument.EndCommandGroup
    ActiveWindow.Refresh
    Exit Sub

ErrorHandler:
    ActiveDocument.EndCommandGroup
    MsgBox "绘制连接线时发生错误：" & Err.Description, vbCritical
End Sub

Sub 选择生成线()
    On Error GoTo ErrorHandler

    Dim targetLines As ShapeRange
    
    If ActiveSelectionRange.Count > 0 Then
        Set targetLines = 筛选生成线(ActiveSelectionRange)
        If targetLines.Count > 0 Then
            ActiveDocument.ClearSelection
            targetLines.CreateSelection
            ActiveWindow.Activate
        Else
            MsgBox "选择范围内未找到名为'" & LINE_NAME & "'的曲线对象！", vbExclamation
        End If
    Else
        Set targetLines = ActivePage.Shapes.FindShapes(Query:="@name='" & LINE_NAME & "' and @type='curve'")
        If targetLines.Count > 0 Then
            ActiveDocument.ClearSelection
            targetLines.CreateSelection
            ActiveWindow.Activate
        Else
            MsgBox "当前文档中未找到名为'" & LINE_NAME & "'的曲线对象！", vbExclamation
        End If
    End If

    ActiveWindow.Refresh
    Exit Sub

ErrorHandler:
    MsgBox "选择生成线时发生错误：" & Err.Description, vbCritical
End Sub

Sub 删除生成线()
    On Error GoTo ErrorHandler

    Dim targetLines As ShapeRange

    If ActiveSelectionRange.Count > 0 Then
        Set targetLines = 筛选生成线(ActiveSelectionRange)
    Else
        Set targetLines = ActivePage.Shapes.FindShapes(Query:="@name='" & LINE_NAME & "' and @type='curve'")
    End If

    If targetLines.Count > 0 Then
        ActiveDocument.BeginCommandGroup "删除生成线"
        targetLines.Delete
        ActiveDocument.EndCommandGroup
        ActiveWindow.Refresh
    Else
        MsgBox "未找到名为'" & LINE_NAME & "'的曲线对象！", vbExclamation
    End If

    Exit Sub

ErrorHandler:
    MsgBox "删除生成线时发生错误：" & Err.Description, vbCritical
End Sub

Sub 增长生成线()
    On Error GoTo ErrorHandler

    Dim targetLines As ShapeRange

    If ActiveSelectionRange.Count > 0 Then
        Set targetLines = 筛选生成线(ActiveSelectionRange)
    Else
        Set targetLines = ActivePage.Shapes.FindShapes(Query:="@name='" & LINE_NAME & "' and @type='curve'")
    End If

    If targetLines.Count > 0 Then
        ActiveDocument.BeginCommandGroup "增长生成线"
        调整线条长度 targetLines, 1.05
        ActiveDocument.EndCommandGroup
        ActiveWindow.Refresh
    Else
        MsgBox "未找到名为'" & LINE_NAME & "'的曲线对象！", vbExclamation
    End If

    Exit Sub

ErrorHandler:
    MsgBox "增长生成线时发生错误：" & Err.Description, vbCritical
End Sub

Sub 缩减生成线()
    On Error GoTo ErrorHandler

    Dim targetLines As ShapeRange

    If ActiveSelectionRange.Count > 0 Then
        Set targetLines = 筛选生成线(ActiveSelectionRange)
    Else
        Set targetLines = ActivePage.Shapes.FindShapes(Query:="@name='" & LINE_NAME & "' and @type='curve'")
    End If

    If targetLines.Count > 0 Then
        ActiveDocument.BeginCommandGroup "缩减生成线"
        调整线条长度 targetLines, 0.95
        ActiveDocument.EndCommandGroup
        ActiveWindow.Refresh
    Else
        MsgBox "未找到名为'" & LINE_NAME & "'的曲线对象！", vbExclamation
    End If

    Exit Sub

ErrorHandler:
    MsgBox "缩减生成线时发生错误：" & Err.Description, vbCritical
End Sub

Private Function 创建连接线(shape1 As Shape, shape2 As Shape) As Shape
    Dim x1 As Double, y1 As Double, w1 As Double, h1 As Double
    Dim x2 As Double, y2 As Double, w2 As Double, h2 As Double

    shape1.GetBoundingBox x1, y1, w1, h1
    shape2.GetBoundingBox x2, y2, w2, h2

    Dim startX As Double, endX As Double, lineY As Double
    startX = x1 + w1 + w1 * LENGTH_ADJUST_PERCENT
    endX = x2 - w2 * LENGTH_ADJUST_PERCENT
    lineY = Min(y1, y2)

    If startX >= endX Then
        Set 创建连接线 = Nothing
        Exit Function
    End If

    Set 创建连接线 = ActiveLayer.CreateLineSegment(startX, lineY, endX, lineY)
    创建连接线.Name = LINE_NAME
End Function

Private Function 计算简单容差(shapes As ShapeRange) As Double
    Dim totalHeight As Double
    Dim i As Integer
    
    For i = 1 To shapes.Count
        Dim x As Double, y As Double, w As Double, h As Double
        shapes(i).GetBoundingBox x, y, w, h
        totalHeight = totalHeight + h
    Next i
    
    计算简单容差 = (totalHeight / shapes.Count) * 0.5
End Function

Private Function 筛选生成线(shapes As ShapeRange) As ShapeRange
    Dim filteredLines As New ShapeRange

    Dim i As Integer
    For i = 1 To shapes.Count
        If shapes(i).Type = cdrCurveShape And shapes(i).Name = LINE_NAME Then
            filteredLines.Add shapes(i)
        End If
    Next i

    Set 筛选生成线 = filteredLines
End Function

Sub 调整线条长度(lines As ShapeRange, factor As Double)
    Dim i As Integer
    For i = 1 To lines.Count
        Dim curve As Shape
        Set curve = lines(i)

        If curve.Type = cdrCurveShape And curve.Curve.Nodes.Count = 2 Then
            Dim startNode As Node
            Dim endNode As Node
            Set startNode = curve.Curve.Nodes(1)
            Set endNode = curve.Curve.Nodes(2)

            Dim centerX As Double, centerY As Double
            centerX = (startNode.PositionX + endNode.PositionX) / 2
            centerY = (startNode.PositionY + endNode.PositionY) / 2

            Dim currentLength As Double
            currentLength = Abs(endNode.PositionX - startNode.PositionX)

            Dim newLength As Double
            newLength = currentLength * factor

            Dim halfLength As Double
            halfLength = newLength / 2

            startNode.PositionX = centerX - halfLength
            startNode.PositionY = centerY
            endNode.PositionX = centerX + halfLength
            endNode.PositionY = centerY
        End If
    Next i
End Sub

Private Function Min(a As Double, b As Double) As Double
    If a < b Then
        Min = a
    Else
        Min = b
    End If
End Function
